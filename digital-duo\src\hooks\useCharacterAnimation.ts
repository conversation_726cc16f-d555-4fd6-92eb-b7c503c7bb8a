import { useState, useEffect, useCallback } from 'react';
import type { AnimationState } from '../types';

interface UseCharacterAnimationProps {
  isSpeaking: boolean;
  isProcessing: boolean;
  currentEmotion?: AnimationState;
}

interface UseCharacterAnimationReturn {
  currentAnimation: AnimationState;
  isAnimating: boolean;
  setAnimation: (animation: AnimationState) => void;
  resetToIdle: () => void;
}

export const useCharacterAnimation = ({
  isSpeaking,
  isProcessing,
  currentEmotion
}: UseCharacterAnimationProps): UseCharacterAnimationReturn => {
  const [currentAnimation, setCurrentAnimation] = useState<AnimationState>('idle');
  const [isAnimating, setIsAnimating] = useState<boolean>(false);
  const [animationQueue, setAnimationQueue] = useState<AnimationState[]>([]);

  // Process animation queue
  const processAnimationQueue = useCallback(() => {
    if (animationQueue.length > 0) {
      const nextAnimation = animationQueue[0];
      setCurrentAnimation(nextAnimation);
      setIsAnimating(true);
      setAnimationQueue(prev => prev.slice(1));
    } else {
      setIsAnimating(false);
    }
  }, [animationQueue]);

  // Set animation with queue management
  const setAnimation = useCallback((animation: AnimationState) => {
    if (animation === currentAnimation) return;
    
    setAnimationQueue(prev => [...prev, animation]);
  }, [currentAnimation]);

  // Reset to idle state
  const resetToIdle = useCallback(() => {
    setAnimationQueue([]);
    setCurrentAnimation('idle');
    setIsAnimating(false);
  }, []);

  // Handle speaking state
  useEffect(() => {
    if (isSpeaking) {
      setAnimation('talk1'); // Start talking animation
    } else if (currentAnimation.startsWith('talk')) {
      // If we were talking and stopped, return to idle or current emotion
      if (currentEmotion && currentEmotion !== 'idle') {
        setAnimation(currentEmotion);
      } else {
        resetToIdle();
      }
    }
  }, [isSpeaking, currentAnimation, currentEmotion, setAnimation, resetToIdle]);

  // Handle processing state
  useEffect(() => {
    if (isProcessing && !isSpeaking) {
      setAnimation('thinking');
    } else if (!isProcessing && currentAnimation === 'thinking') {
      if (currentEmotion && currentEmotion !== 'idle') {
        setAnimation(currentEmotion);
      } else {
        resetToIdle();
      }
    }
  }, [isProcessing, isSpeaking, currentAnimation, currentEmotion, setAnimation, resetToIdle]);

  // Handle emotion changes
  useEffect(() => {
    if (currentEmotion && !isSpeaking && !isProcessing) {
      setAnimation(currentEmotion);
    }
  }, [currentEmotion, isSpeaking, isProcessing, setAnimation]);

  // Process animation queue when it changes
  useEffect(() => {
    if (!isAnimating && animationQueue.length > 0) {
      processAnimationQueue();
    }
  }, [animationQueue, isAnimating, processAnimationQueue]);

  // Animation completion handler
  const handleAnimationComplete = useCallback(() => {
    setIsAnimating(false);
    
    // If there are more animations in queue, process them
    if (animationQueue.length > 0) {
      setTimeout(processAnimationQueue, 100); // Small delay between animations
    } else {
      // No more animations, determine next state
      if (isSpeaking) {
        setCurrentAnimation('talk1');
      } else if (isProcessing) {
        setCurrentAnimation('thinking');
      } else if (currentEmotion && currentEmotion !== 'idle') {
        setCurrentAnimation(currentEmotion);
      } else {
        setCurrentAnimation('idle');
      }
    }
  }, [animationQueue, isSpeaking, isProcessing, currentEmotion, processAnimationQueue]);

  // Auto-complete animations after a timeout
  useEffect(() => {
    if (isAnimating) {
      const timeout = setTimeout(() => {
        handleAnimationComplete();
      }, 2000); // Max animation duration

      return () => clearTimeout(timeout);
    }
  }, [isAnimating, handleAnimationComplete]);

  return {
    currentAnimation,
    isAnimating,
    setAnimation,
    resetToIdle
  };
};

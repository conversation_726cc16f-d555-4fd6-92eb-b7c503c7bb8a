import { useState, useEffect, useCallback, useRef } from 'react';
import type { AnimationState } from '../types';

interface UseCharacterAnimationProps {
  isSpeaking: boolean;
  isProcessing: boolean;
  currentEmotion?: AnimationState;
}

interface UseCharacterAnimationReturn {
  currentAnimation: AnimationState;
  isAnimating: boolean;
  setAnimation: (animation: AnimationState) => void;
  resetToIdle: () => void;
}

export const useCharacterAnimation = ({
  isSpeaking,
  isProcessing,
  currentEmotion
}: UseCharacterAnimationProps): UseCharacterAnimationReturn => {
  const [currentAnimation, setCurrentAnimation] = useState<AnimationState>('idle');
  const [isAnimating, setIsAnimating] = useState<boolean>(false);
  const [animationQueue, setAnimationQueue] = useState<AnimationState[]>([]);
  const talkingIntervalRef = useRef<number | null>(null);

  // Process animation queue
  const processAnimationQueue = useCallback(() => {
    if (animationQueue.length > 0) {
      const nextAnimation = animationQueue[0];
      setCurrentAnimation(nextAnimation);
      setIsAnimating(true);
      setAnimationQueue(prev => prev.slice(1));
    } else {
      setIsAnimating(false);
    }
  }, [animationQueue]);

  // Set animation with queue management
  const setAnimation = useCallback((animation: AnimationState) => {
    if (animation === currentAnimation) return;

    // Don't queue animations during talking cycle
    if (talkingIntervalRef.current && animation.startsWith('talk')) {
      console.log('🚫 Ignoring animation change during talking cycle:', animation);
      return;
    }

    setAnimationQueue(prev => [...prev, animation]);
  }, [currentAnimation]);

  // Reset to idle state
  const resetToIdle = useCallback(() => {
    setAnimationQueue([]);
    setCurrentAnimation('idle');
    setIsAnimating(false);
    // Clear talking animation cycle
    if (talkingIntervalRef.current) {
      clearInterval(talkingIntervalRef.current);
      talkingIntervalRef.current = null;
    }
  }, []);

  // Handle speaking state
  useEffect(() => {
    if (isSpeaking) {
      console.log('🗣️ ARIA started speaking - starting talking animation cycle');

      // Clear any existing interval
      if (talkingIntervalRef.current) {
        clearInterval(talkingIntervalRef.current);
      }

      // Clear animation queue
      setAnimationQueue([]);
      setIsAnimating(false);

      // Start with talk1
      setCurrentAnimation('talk1');
      let cycleIndex = 0;

      // Create interval to cycle through talk animations
      talkingIntervalRef.current = window.setInterval(() => {
        cycleIndex = (cycleIndex + 1) % 3; // Cycle through 0, 1, 2
        const talkAnimations: AnimationState[] = ['talk1', 'talk2', 'talk3'];
        const nextAnimation = talkAnimations[cycleIndex];
        console.log(`🎭 Cycling to talking animation: ${nextAnimation} (index: ${cycleIndex})`);
        setCurrentAnimation(nextAnimation);
      }, 300); // Back to 300ms for natural timing

    } else if (talkingIntervalRef.current) {
      console.log('🤐 ARIA stopped speaking - stopping talking animation cycle');

      // Clear the talking interval
      clearInterval(talkingIntervalRef.current);
      talkingIntervalRef.current = null;

      // Return to idle or current emotion
      if (currentEmotion && currentEmotion !== 'idle') {
        setCurrentAnimation(currentEmotion);
      } else {
        setCurrentAnimation('idle');
      }
    }
  }, [isSpeaking, currentEmotion]);

  // Handle processing state
  useEffect(() => {
    if (isProcessing && !isSpeaking) {
      setAnimation('thinking');
    } else if (!isProcessing && currentAnimation === 'thinking') {
      if (currentEmotion && currentEmotion !== 'idle') {
        setAnimation(currentEmotion);
      } else {
        resetToIdle();
      }
    }
  }, [isProcessing, isSpeaking, currentAnimation, currentEmotion, setAnimation, resetToIdle]);

  // Handle emotion changes
  useEffect(() => {
    if (currentEmotion && !isSpeaking && !isProcessing) {
      setAnimation(currentEmotion);
    }
  }, [currentEmotion, isSpeaking, isProcessing, setAnimation]);

  // Process animation queue when it changes (but not during talking)
  useEffect(() => {
    if (!isAnimating && animationQueue.length > 0 && !talkingIntervalRef.current) {
      processAnimationQueue();
    }
  }, [animationQueue, isAnimating, processAnimationQueue]);

  // Animation completion handler
  const handleAnimationComplete = useCallback(() => {
    setIsAnimating(false);

    // If there are more animations in queue, process them
    if (animationQueue.length > 0) {
      setTimeout(processAnimationQueue, 100); // Small delay between animations
    } else {
      // No more animations, determine next state
      // Don't interfere if we're in a talking cycle
      if (isSpeaking && talkingIntervalRef.current) {
        // Let the talking cycle handle the animation
        return;
      } else if (isProcessing) {
        setCurrentAnimation('thinking');
      } else if (currentEmotion && currentEmotion !== 'idle') {
        setCurrentAnimation(currentEmotion);
      } else {
        setCurrentAnimation('idle');
      }
    }
  }, [animationQueue, isSpeaking, isProcessing, currentEmotion, processAnimationQueue]);

  // Auto-complete animations after a timeout
  useEffect(() => {
    if (isAnimating) {
      const timeout = setTimeout(() => {
        handleAnimationComplete();
      }, 2000); // Max animation duration

      return () => clearTimeout(timeout);
    }
  }, [isAnimating, handleAnimationComplete]);

  // Cleanup talking interval on unmount
  useEffect(() => {
    return () => {
      if (talkingIntervalRef.current) {
        clearInterval(talkingIntervalRef.current);
      }
    };
  }, []);

  return {
    currentAnimation,
    isAnimating,
    setAnimation,
    resetToIdle
  };
};

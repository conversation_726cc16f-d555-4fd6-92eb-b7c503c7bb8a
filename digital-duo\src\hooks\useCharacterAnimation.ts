import { useState, useEffect, useCallback } from 'react';
import type { AnimationState } from '../types';

interface UseCharacterAnimationProps {
  isSpeaking: boolean;
  isProcessing: boolean;
  currentEmotion?: AnimationState;
}

interface UseCharacterAnimationReturn {
  currentAnimation: AnimationState;
  isAnimating: boolean;
  setAnimation: (animation: AnimationState) => void;
  resetToIdle: () => void;
}

export const useCharacterAnimation = ({
  isSpeaking,
  isProcessing,
  currentEmotion
}: UseCharacterAnimationProps): UseCharacterAnimationReturn => {
  const [currentAnimation, setCurrentAnimation] = useState<AnimationState>('idle');
  const [isAnimating, setIsAnimating] = useState<boolean>(false);
  const [animationQueue, setAnimationQueue] = useState<AnimationState[]>([]);
  const [talkingIntervalId, setTalkingIntervalId] = useState<number | null>(null);

  // Process animation queue
  const processAnimationQueue = useCallback(() => {
    if (animationQueue.length > 0) {
      const nextAnimation = animationQueue[0];
      setCurrentAnimation(nextAnimation);
      setIsAnimating(true);
      setAnimationQueue(prev => prev.slice(1));
    } else {
      setIsAnimating(false);
    }
  }, [animationQueue]);

  // Set animation with queue management
  const setAnimation = useCallback((animation: AnimationState) => {
    if (animation === currentAnimation) return;
    
    setAnimationQueue(prev => [...prev, animation]);
  }, [currentAnimation]);

  // Reset to idle state
  const resetToIdle = useCallback(() => {
    setAnimationQueue([]);
    setCurrentAnimation('idle');
    setIsAnimating(false);
    // Clear talking animation cycle
    if (talkingIntervalId) {
      clearInterval(talkingIntervalId);
      setTalkingIntervalId(null);
    }
  }, [talkingIntervalId]);

  // Start talking animation cycle
  const startTalkingCycle = useCallback(() => {
    // Clear any existing interval
    if (talkingIntervalId) {
      clearInterval(talkingIntervalId);
    }

    // Start with talk1
    setCurrentAnimation('talk1');
    let cycleIndex = 0;

    // Create interval to cycle through talk animations
    const intervalId = window.setInterval(() => {
      cycleIndex = (cycleIndex + 1) % 3; // Cycle through 0, 1, 2
      const talkAnimations: AnimationState[] = ['talk1', 'talk2', 'talk3'];
      const nextAnimation = talkAnimations[cycleIndex];
      console.log(`Cycling to talking animation: ${nextAnimation}`);
      setCurrentAnimation(nextAnimation);
    }, 300); // Change animation every 300ms

    setTalkingIntervalId(intervalId);
  }, [talkingIntervalId]);

  // Stop talking animation cycle
  const stopTalkingCycle = useCallback(() => {
    if (talkingIntervalId) {
      clearInterval(talkingIntervalId);
      setTalkingIntervalId(null);
    }
  }, [talkingIntervalId]);

  // Handle speaking state
  useEffect(() => {
    if (isSpeaking) {
      console.log('Starting talking animation cycle');
      startTalkingCycle();
    } else {
      console.log('Stopping talking animation cycle');
      stopTalkingCycle();

      // If we were talking and stopped, return to idle or current emotion
      if (currentAnimation.startsWith('talk')) {
        if (currentEmotion && currentEmotion !== 'idle') {
          setAnimation(currentEmotion);
        } else {
          resetToIdle();
        }
      }
    }
  }, [isSpeaking, currentAnimation, currentEmotion, setAnimation, resetToIdle, startTalkingCycle, stopTalkingCycle]);

  // Handle processing state
  useEffect(() => {
    if (isProcessing && !isSpeaking) {
      setAnimation('thinking');
    } else if (!isProcessing && currentAnimation === 'thinking') {
      if (currentEmotion && currentEmotion !== 'idle') {
        setAnimation(currentEmotion);
      } else {
        resetToIdle();
      }
    }
  }, [isProcessing, isSpeaking, currentAnimation, currentEmotion, setAnimation, resetToIdle]);

  // Handle emotion changes
  useEffect(() => {
    if (currentEmotion && !isSpeaking && !isProcessing) {
      setAnimation(currentEmotion);
    }
  }, [currentEmotion, isSpeaking, isProcessing, setAnimation]);

  // Process animation queue when it changes
  useEffect(() => {
    if (!isAnimating && animationQueue.length > 0) {
      processAnimationQueue();
    }
  }, [animationQueue, isAnimating, processAnimationQueue]);

  // Animation completion handler
  const handleAnimationComplete = useCallback(() => {
    setIsAnimating(false);

    // If there are more animations in queue, process them
    if (animationQueue.length > 0) {
      setTimeout(processAnimationQueue, 100); // Small delay between animations
    } else {
      // No more animations, determine next state
      // Don't interfere if we're in a talking cycle
      if (isSpeaking && talkingIntervalId) {
        // Let the talking cycle handle the animation
        return;
      } else if (isSpeaking) {
        startTalkingCycle();
      } else if (isProcessing) {
        setCurrentAnimation('thinking');
      } else if (currentEmotion && currentEmotion !== 'idle') {
        setCurrentAnimation(currentEmotion);
      } else {
        setCurrentAnimation('idle');
      }
    }
  }, [animationQueue, isSpeaking, isProcessing, currentEmotion, processAnimationQueue, talkingIntervalId, startTalkingCycle]);

  // Auto-complete animations after a timeout
  useEffect(() => {
    if (isAnimating) {
      const timeout = setTimeout(() => {
        handleAnimationComplete();
      }, 2000); // Max animation duration

      return () => clearTimeout(timeout);
    }
  }, [isAnimating, handleAnimationComplete]);

  // Cleanup talking interval on unmount
  useEffect(() => {
    return () => {
      if (talkingIntervalId) {
        clearInterval(talkingIntervalId);
      }
    };
  }, [talkingIntervalId]);

  return {
    currentAnimation,
    isAnimating,
    setAnimation,
    resetToIdle
  };
};

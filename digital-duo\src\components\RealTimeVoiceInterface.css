.realtime-voice-interface {
  background: rgba(0, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 10px;
  font-family: 'Courier New', monospace;
  color: #00ffff;
  transition: all 0.3s ease;
}

.realtime-voice-interface.collapsed {
  height: auto;
}

.realtime-voice-interface.expanded {
  height: auto;
  max-height: 700px;
  display: flex;
  flex-direction: column;
}

.voice-header {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  user-select: none;
  transition: background-color 0.3s ease;
}

.voice-header:hover {
  background: rgba(0, 255, 255, 0.1);
}

.voice-header h3 {
  margin: 0;
  color: #00ffff;
  font-size: 1.2rem;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.voice-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  font-size: 1.5rem;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  min-width: 40px;
  text-align: center;
}

.status-indicator.listening {
  background: rgba(0, 255, 0, 0.2);
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
  animation: pulse-listening 2s infinite;
}

.status-indicator.awake {
  background: rgba(255, 255, 0, 0.2);
  box-shadow: 0 0 10px rgba(255, 255, 0, 0.5);
  animation: pulse-awake 1.5s infinite;
}

.status-indicator.speaking {
  background: rgba(255, 165, 0, 0.2);
  box-shadow: 0 0 10px rgba(255, 165, 0, 0.5);
  animation: pulse-speaking 0.5s infinite;
}

.status-indicator.processing {
  background: rgba(255, 0, 255, 0.2);
  box-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
  animation: pulse-processing 0.8s infinite;
}

.status-text {
  font-size: 0.9rem;
  color: #88ffff;
  font-weight: bold;
}

.voice-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.permission-request,
.permission-denied {
  background: rgba(255, 165, 0, 0.1);
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 8px;
  padding: 15px;
  text-align: center;
}

.permission-request p,
.permission-denied p {
  margin: 0 0 10px 0;
  color: #ffaa00;
}

.permission-button {
  background: linear-gradient(135deg, #ffaa00 0%, #ff8800 100%);
  border: none;
  color: #000;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  transition: all 0.3s ease;
}

.permission-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 170, 0, 0.4);
}

.voice-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}

.main-control {
  display: flex;
  justify-content: center;
}

.voice-toggle {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border: 2px solid #00ffff;
  color: #00ffff;
  padding: 15px 30px;
  border-radius: 25px;
  cursor: pointer;
  font-family: 'Courier New', monospace;
  font-size: 1.1rem;
  font-weight: bold;
  transition: all 0.3s ease;
  min-width: 150px;
}

.voice-toggle.enabled {
  background: linear-gradient(135deg, #00ffff 0%, #0088ff 100%);
  color: #000;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.voice-toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0, 255, 255, 0.3);
}

.wake-controls {
  display: flex;
  gap: 10px;
}

.wake-button,
.sleep-button {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border: 2px solid #88ffff;
  color: #88ffff;
  padding: 8px 16px;
  border-radius: 15px;
  cursor: pointer;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.wake-button:hover:not(:disabled),
.sleep-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #88ffff 0%, #0088ff 100%);
  color: #000;
  transform: translateY(-1px);
}

.wake-button:disabled,
.sleep-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.current-transcript {
  background: rgba(255, 255, 0, 0.1);
  border: 1px solid rgba(255, 255, 0, 0.3);
  border-radius: 8px;
  padding: 15px;
  animation: fadeIn 0.3s ease;
}

.current-transcript h4 {
  margin: 0 0 10px 0;
  color: #ffff00;
  font-size: 1rem;
}

.current-transcript p {
  margin: 0;
  color: #ffffaa;
  font-style: italic;
  font-size: 1.1rem;
  line-height: 1.4;
}

.wake-word-info {
  background: rgba(0, 255, 0, 0.1);
  border: 1px solid rgba(0, 255, 0, 0.3);
  border-radius: 8px;
  padding: 15px;
}

.wake-word-info h4 {
  margin: 0 0 10px 0;
  color: #88ff88;
  font-size: 1rem;
}

.command-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.command-item {
  color: #aaffaa;
  font-size: 0.9rem;
  line-height: 1.4;
}

.command-item strong {
  color: #88ff88;
}

.voice-settings {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.setting-group label {
  font-size: 0.9rem;
  color: #00ffff;
}

.setting-group select {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 4px;
  color: #00ffff;
  padding: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.setting-group select:focus {
  outline: none;
  border-color: #00ffff;
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.setting-group input[type="range"] {
  width: 100%;
  height: 6px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 3px;
  outline: none;
  border: 1px solid rgba(0, 255, 255, 0.3);
}

.setting-group input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: #00ffff;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.setting-group input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #00ffff;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.setting-group input:disabled,
.setting-group select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Animations */
@keyframes pulse-listening {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes pulse-awake {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes pulse-speaking {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.1);
  }
}

@keyframes pulse-processing {
  0%, 100% {
    opacity: 1;
    transform: rotate(0deg);
  }
  50% {
    opacity: 0.7;
    transform: rotate(180deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .voice-content {
    padding: 15px;
  }
  
  .voice-toggle {
    padding: 12px 24px;
    font-size: 1rem;
  }
  
  .wake-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .wake-button,
  .sleep-button {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .voice-header {
    padding: 12px 15px;
  }
  
  .voice-content {
    padding: 10px;
  }
  
  .voice-toggle {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
  
  .current-transcript p {
    font-size: 1rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .status-indicator.listening,
  .status-indicator.awake,
  .status-indicator.speaking,
  .status-indicator.processing {
    animation: none;
  }
  
  .current-transcript {
    animation: none;
  }
  
  .voice-toggle:hover,
  .wake-button:hover,
  .sleep-button:hover,
  .permission-button:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .realtime-voice-interface {
    border-color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .voice-header h3,
  .setting-group label,
  .status-text {
    color: #ffffff;
    text-shadow: none;
  }
  
  .setting-group input[type="range"],
  .setting-group select {
    border-color: #ffffff;
  }
}

import { useState, useCallback, useRef, useEffect } from 'react';
import type { AIMessage, ConversationHistory, CharacterPersonality, AnimationState, AISettings } from '../types';
import { aiService } from '../services/aiService';
import { getPersonalityPrompt, detectEmotionFromText } from '../utils/characters';

interface UseAIConversationProps {
  character: CharacterPersonality;
  aiSettings: AISettings;
  onAnimationChange: (animation: AnimationState) => void;
  onSpeakText: (text: string) => void;
}

interface UseAIConversationReturn {
  messages: AIMessage[];
  isProcessing: boolean;
  connectionStatus: 'connected' | 'disconnected' | 'connecting';
  error: string | null;
  sendMessage: (content: string, includeVideo?: ImageData) => Promise<void>;
  clearConversation: () => void;
  getConversationHistory: () => ConversationHistory;
}

export const useAIConversation = ({
  character,
  aiSettings,
  onAnimationChange,
  onSpeakText
}: UseAIConversationProps): UseAIConversationReturn => {
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');
  const [error, setError] = useState<string | null>(null);
  
  const sessionIdRef = useRef<string>(generateSessionId());
  const conversationStartRef = useRef<Date>(new Date());

  // Update AI service configuration when settings change
  useEffect(() => {
    if (aiSettings && aiSettings.provider) {
      aiService.updateConfig({
        provider: aiSettings.provider,
        gemini: aiSettings.gemini,
        ollama: aiSettings.ollama,
      });
    }
  }, [aiSettings]);

  // Generate unique session ID
  function generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Generate unique message ID
  const generateMessageId = useCallback((): string => {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Check API connection
  const checkConnection = useCallback(async () => {
    setConnectionStatus('connecting');
    try {
      const isConnected = await aiService.checkConnection();
      setConnectionStatus(isConnected ? 'connected' : 'disconnected');
      if (!isConnected) {
        const providerName = aiSettings?.provider === 'gemini' ? 'Google Gemini' : 'Ollama';
        setError(`Unable to connect to ${providerName} service`);
      } else {
        setError(null);
      }
    } catch (err) {
      setConnectionStatus('disconnected');
      setError('Connection check failed');
    }
  }, [aiSettings?.provider]);

  // Add message to conversation
  const addMessage = useCallback((content: string, sender: 'user' | 'ai', emotion?: AnimationState): AIMessage => {
    const message: AIMessage = {
      id: generateMessageId(),
      content,
      timestamp: new Date(),
      sender,
      characterId: character.id,
      emotion
    };

    setMessages(prev => [...prev, message]);
    return message;
  }, [character.id, generateMessageId]);

  // Process AI response and trigger animations/speech
  const processAIResponse = useCallback((response: string) => {
    // Detect emotion from response
    const emotion = detectEmotionFromText(response);
    
    // Add AI message
    addMessage(response, 'ai', emotion);
    
    // Trigger character animation
    onAnimationChange(emotion);
    
    // Trigger text-to-speech
    onSpeakText(response);
    
    return emotion;
  }, [addMessage, onAnimationChange, onSpeakText]);

  // Send message to AI
  const sendMessage = useCallback(async (content: string, includeVideo?: ImageData) => {
    if (!content.trim() || isProcessing) return;

    setIsProcessing(true);
    setError(null);

    try {
      // Add user message
      addMessage(content, 'user');

      // Set thinking animation
      onAnimationChange('thinking');

      // Prepare system prompt with character personality
      const systemPrompt = getPersonalityPrompt(character);

      // Build conversation context (last 10 messages for context)
      const recentMessages = messages.slice(-10);
      const conversationContext = recentMessages
        .map(msg => `${msg.sender === 'user' ? 'User' : character.name}: ${msg.content}`)
        .join('\n');

      const fullPrompt = conversationContext 
        ? `Previous conversation:\n${conversationContext}\n\nUser: ${content}`
        : content;

      let response: string;

      // Generate response with or without image
      if (includeVideo) {
        response = await aiService.generateWithImage(
          fullPrompt,
          includeVideo,
          systemPrompt,
          {
            temperature: 0.8,
            maxTokens: 512,
            includeContext: true
          }
        );
      } else {
        response = await aiService.generateText(
          fullPrompt,
          systemPrompt,
          {
            temperature: 0.8,
            maxTokens: 512,
            includeContext: true
          }
        );
      }

      if (!response.trim()) {
        throw new Error('Empty response from AI service');
      }

      // Process the AI response
      processAIResponse(response.trim());

      // Update connection status
      setConnectionStatus('connected');

    } catch (err: any) {
      console.error('AI conversation error:', err);
      setError(err.message || 'Failed to get AI response');
      
      // Add error message
      addMessage(
        "I'm sorry, I'm having trouble responding right now. Please try again.",
        'ai',
        'confused'
      );
      
      onAnimationChange('confused');
      setConnectionStatus('disconnected');
    } finally {
      setIsProcessing(false);
    }
  }, [
    isProcessing,
    messages,
    character,
    addMessage,
    onAnimationChange,
    processAIResponse
  ]);

  // Clear conversation
  const clearConversation = useCallback(() => {
    setMessages([]);
    setError(null);
    sessionIdRef.current = generateSessionId();
    conversationStartRef.current = new Date();
  }, []);

  // Get conversation history
  const getConversationHistory = useCallback((): ConversationHistory => {
    return {
      messages,
      characterId: character.id,
      sessionId: sessionIdRef.current,
      startTime: conversationStartRef.current,
      lastActivity: messages.length > 0 ? messages[messages.length - 1].timestamp : conversationStartRef.current
    };
  }, [messages, character.id]);

  // Initialize connection check with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      checkConnection();
    }, 1000); // Debounce connection checks to avoid API limits

    return () => clearTimeout(timeoutId);
  }, [checkConnection]);

  return {
    messages,
    isProcessing,
    connectionStatus,
    error,
    sendMessage,
    clearConversation,
    getConversationHistory
  };
};

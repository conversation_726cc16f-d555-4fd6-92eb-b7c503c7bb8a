:root {
  font-family: 'Courier New', monospace;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: dark;
  color: #00ffff;
  background-color: #0a0a0a;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  overflow-x: hidden;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #00ffff 0%, #0088ff 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #00cccc 0%, #0066cc 100%);
}

/* Selection styling */
::selection {
  background: rgba(0, 255, 255, 0.3);
  color: #ffffff;
}

/* Focus styling */
button:focus,
button:focus-visible {
  outline: 2px solid #00ffff;
  outline-offset: 2px;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  :root {
    color: #ffffff;
    background-color: #000000;
  }

  body {
    background: #000000;
  }
}

/* Print styles */
@media print {
  body {
    background: white;
    color: black;
  }

  .app {
    background: white;
    color: black;
  }
}

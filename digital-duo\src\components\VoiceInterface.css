.voice-interface {
  background: rgba(0, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 10px;
  padding: 20px;
  font-family: 'Courier New', monospace;
  color: #00ffff;
}

.voice-interface.error {
  border-color: rgba(255, 0, 0, 0.5);
  background: rgba(255, 0, 0, 0.05);
}

.voice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.voice-header h3 {
  margin: 0;
  color: #00ffff;
  font-size: 1.2rem;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.voice-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  font-size: 1.2rem;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.status-indicator.listening {
  background: rgba(0, 255, 0, 0.2);
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
  animation: pulse-listening 1s infinite;
}

.status-indicator.speaking {
  background: rgba(255, 165, 0, 0.2);
  box-shadow: 0 0 10px rgba(255, 165, 0, 0.5);
  animation: pulse-speaking 0.5s infinite;
}

.status-text {
  font-size: 0.9rem;
  color: #88ffff;
}

.error-message {
  background: rgba(255, 0, 0, 0.1);
  border: 1px solid rgba(255, 0, 0, 0.3);
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 15px;
  color: #ff6666;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message button {
  background: none;
  border: none;
  color: #ff6666;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
}

.audio-visualizer {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 255, 0.1);
}

.audio-level-container {
  width: 100%;
  height: 20px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 8px;
  border: 1px solid rgba(0, 255, 255, 0.3);
}

.audio-level-bar {
  height: 100%;
  background: linear-gradient(90deg, #00ff00 0%, #ffff00 50%, #ff0000 100%);
  transition: width 0.1s ease;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.audio-level-text {
  font-size: 0.8rem;
  color: #88ffff;
  display: block;
  text-align: center;
}

.voice-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.control-group label {
  font-size: 0.9rem;
  color: #00ffff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #00ffff;
}

.control-group input[type="range"] {
  width: 100%;
  height: 6px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 3px;
  outline: none;
  border: 1px solid rgba(0, 255, 255, 0.3);
}

.control-group input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: #00ffff;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.control-group input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #00ffff;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.control-group select {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 4px;
  color: #00ffff;
  padding: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.control-group select:focus {
  outline: none;
  border-color: #00ffff;
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.control-group input:disabled,
.control-group select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Animations */
@keyframes pulse-listening {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes pulse-speaking {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .voice-interface {
    padding: 15px;
  }
  
  .voice-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .voice-status {
    align-self: flex-end;
  }
  
  .control-group label {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .voice-interface {
    padding: 10px;
  }
  
  .audio-level-container {
    height: 16px;
  }
  
  .control-group {
    gap: 3px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .status-indicator.listening,
  .status-indicator.speaking {
    animation: none;
  }
  
  .audio-level-bar {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .voice-interface {
    border-color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .voice-header h3,
  .control-group label,
  .status-text {
    color: #ffffff;
    text-shadow: none;
  }
  
  .control-group input[type="range"],
  .control-group select {
    border-color: #ffffff;
  }
}

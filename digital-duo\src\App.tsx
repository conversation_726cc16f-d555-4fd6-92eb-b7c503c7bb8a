import { useState } from 'react';
import CharacterDisplay from './components/CharacterDisplay';
import { useCharacterAnimation } from './hooks/useCharacterAnimation';
import { defaultCharacters, getDefaultCharacter } from './utils/characters';
import type { AnimationState } from './types';
import './App.css';

function App() {
  console.log('App component rendering...');

  const [currentCharacter, setCurrentCharacter] = useState(() => {
    console.log('Getting default character...');
    const char = getDefaultCharacter();
    console.log('Default character:', char);
    return char;
  });

  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentEmotion] = useState<AnimationState>('idle');

  const { currentAnimation, isAnimating, setAnimation, resetToIdle } = useCharacterAnimation({
    isSpeaking,
    isProcessing,
    currentEmotion
  });

  console.log('Current character:', currentCharacter);
  console.log('Current animation:', currentAnimation);



  const simulateSpeaking = () => {
    setIsSpeaking(true);
    setTimeout(() => setIsSpeaking(false), 3000);
  };

  const simulateProcessing = () => {
    setIsProcessing(true);
    setTimeout(() => setIsProcessing(false), 2000);
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>Digital Duo - AI Companion</h1>
        <p>Megaman Battle Network P.E.T. Inspired Interface</p>
      </header>

      <main className="app-main">
        <div className="character-section">
          <CharacterDisplay
            character={currentCharacter}
            currentAnimation={currentAnimation}
            isAnimating={isAnimating}
          />
        </div>

        <div className="controls-section">
          <div className="animation-controls">
            <h3>Test Animations:</h3>
            <button onClick={() => setAnimation('happy')}>Happy</button>
            <button onClick={() => setAnimation('excited')}>Excited</button>
            <button onClick={() => setAnimation('thinking')}>Thinking</button>
            <button onClick={() => setAnimation('confused')}>Confused</button>
            <button onClick={() => setAnimation('blink')}>Blink</button>
            <button onClick={resetToIdle}>Reset to Idle</button>
          </div>

          <div className="state-controls">
            <h3>Test States:</h3>
            <button onClick={simulateSpeaking} disabled={isSpeaking}>
              {isSpeaking ? 'Speaking...' : 'Simulate Speaking'}
            </button>
            <button onClick={simulateProcessing} disabled={isProcessing}>
              {isProcessing ? 'Processing...' : 'Simulate Processing'}
            </button>
          </div>

          <div className="character-info">
            <h3>Character Info:</h3>
            <p><strong>Name:</strong> {currentCharacter.name}</p>
            <p><strong>Description:</strong> {currentCharacter.description}</p>
            <p><strong>Specializations:</strong> {currentCharacter.specialization}</p>
            <p><strong>Communication Style:</strong> {currentCharacter.communicationStyle}</p>
            <p><strong>Key Traits:</strong> {currentCharacter.traits.join(', ')}</p>
            <p><strong>Current Animation:</strong> {currentAnimation}</p>
            <p><strong>Is Animating:</strong> {isAnimating ? 'Yes' : 'No'}</p>
          </div>
        </div>
      </main>
    </div>
  );
}

export default App;

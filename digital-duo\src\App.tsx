import { useState, useCallback } from 'react';
import CharacterDisplay from './components/CharacterDisplay';
import RealTimeVoiceInterface from './components/RealTimeVoiceInterface';
import VideoFeed from './components/VideoFeed';
import ChatInterface from './components/ChatInterface';
import SettingsPanel from './components/SettingsPanel';
import { useCharacterAnimation } from './hooks/useCharacterAnimation';
import { useRealTimeVoice } from './hooks/useRealTimeVoice';
import { useAIConversation } from './hooks/useAIConversation';
import { useUserPreferences, useUserAnalytics } from './hooks/useLocalStorage';
import { defaultCharacters, getDefaultCharacter } from './utils/characters';
import type { AnimationState } from './types';
import './App.css';

function App() {
  console.log('App component rendering...');

  // Local storage hooks
  const { preferences, setPreferences } = useUserPreferences();
  const { trackInteraction } = useUserAnalytics();

  // Ensure preferences are loaded before rendering
  if (!preferences || !preferences.aiSettings) {
    return (
      <div className="app">
        <div className="loading-screen">
          <h1>Loading Digital Duo...</h1>
          <p>Initializing AI companion...</p>
        </div>
      </div>
    );
  }

  const [currentCharacter] = useState(() => {
    const char = getDefaultCharacter();
    console.log('Default character:', char);
    return char;
  });

  const [currentEmotion, setCurrentEmotion] = useState<AnimationState>('idle');

  // Real-time voice interface hook
  const {
    isListening,
    isSpeaking,
    isAwake,
    isProcessing: voiceProcessing,
    currentTranscript,
    speak,
    wakeUp,
    sleep
  } = useRealTimeVoice({
    voiceSettings: preferences.voiceSettings,
    onWakeWordDetected: () => {
      console.log('Wake word detected - ARIA is now awake');
      trackInteraction('wake_word', {});
      speak("Hello! I'm ARIA, your AI companion. How can I help you today?");
    },
    onGoodbyeDetected: () => {
      console.log('Goodbye detected - ARIA is going to sleep');
      trackInteraction('goodbye_word', {});
      speak("Goodbye! Say 'Hello ARIA' to wake me up again.");
    },
    onVoiceInput: (text: string) => {
      console.log('Voice input received:', text);
      trackInteraction('voice_input', { text });
      sendMessage(text, preferences.videoSettings.enabled && preferences.videoSettings.cameraEnabled ? currentVideoFrame : undefined);
    },
    onSpeakingStateChange: (speaking: boolean) => {
      console.log('Speaking state changed:', speaking);
      trackInteraction('speech', { speaking });
    }
  });

  // AI conversation hook
  const {
    messages,
    isProcessing,
    connectionStatus,
    error: aiError,
    sendMessage,
    clearConversation
  } = useAIConversation({
    character: currentCharacter,
    aiSettings: preferences.aiSettings,
    onAnimationChange: (animation: AnimationState) => {
      setCurrentEmotion(animation);
      setAnimation(animation);
      trackInteraction('emotion', { emotion: animation });
    },
    onSpeakText: (text: string) => {
      speak(text);
      trackInteraction('ai_speech', { text });
    }
  });

  const [currentVideoFrame, setCurrentVideoFrame] = useState<ImageData | undefined>();

  // Character animation hook
  const { currentAnimation, isAnimating, setAnimation } = useCharacterAnimation({
    isSpeaking,
    isProcessing: isProcessing || voiceProcessing,
    currentEmotion
  });



  // Handle video frame updates
  const handleVideoFrame = useCallback((imageData: ImageData) => {
    setCurrentVideoFrame(imageData);
  }, []);

  // Handle manual message sending
  const handleSendMessage = useCallback((text: string) => {
    trackInteraction('text_input', { text });
    sendMessage(text, preferences.videoSettings.enabled && preferences.videoSettings.cameraEnabled ? currentVideoFrame : undefined);
  }, [sendMessage, preferences.videoSettings.enabled, preferences.videoSettings.cameraEnabled, currentVideoFrame, trackInteraction]);

  // Handle character change
  const handleCharacterChange = useCallback((characterId: string) => {
    // Since we only have one character, this is mainly for future extensibility
    trackInteraction('character_change', { characterId });
  }, [trackInteraction]);

  // Voice control functions
  const toggleVoice = () => {
    const newSettings = {
      ...preferences.voiceSettings,
      enabled: !preferences.voiceSettings.enabled
    };
    setPreferences({
      ...preferences,
      voiceSettings: newSettings
    });
  };

  // Test functions for development
  const simulateSpeaking = () => {
    speak("Hello! This is a test of the text-to-speech functionality.");
  };

  const simulateProcessing = () => {
    setCurrentEmotion('thinking');
    setAnimation('thinking');
    setTimeout(() => {
      setCurrentEmotion('idle');
      setAnimation('idle');
    }, 2000);
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>Digital Duo - AI Companion</h1>
        <p>Megaman Battle Network P.E.T. Inspired Interface</p>
        <div className="connection-status">
          <span className={`status-dot ${connectionStatus}`}></span>
          <span className="status-text">
            {connectionStatus === 'connected' ?
              `${preferences.aiSettings?.provider === 'gemini' ? 'Gemini' : 'Ollama'} Connected` :
             connectionStatus === 'connecting' ? 'Connecting...' :
              `${preferences.aiSettings?.provider === 'gemini' ? 'Gemini' : 'Ollama'} Disconnected`}
          </span>
        </div>
      </header>

      <main className="app-main">
        <div className="left-panel">
          <div className="character-section">
            <CharacterDisplay
              character={currentCharacter}
              currentAnimation={currentAnimation}
              isAnimating={isAnimating}
            />
          </div>

          <div className="interface-controls">
            <RealTimeVoiceInterface
              voiceSettings={preferences.voiceSettings}
              onSettingsChange={(settings) => setPreferences({
                ...preferences,
                voiceSettings: settings
              })}
              isListening={isListening}
              isSpeaking={isSpeaking}
              isAwake={isAwake}
              isProcessing={voiceProcessing}
              currentTranscript={currentTranscript}
              onToggleVoice={toggleVoice}
              onWakeUp={wakeUp}
              onSleep={sleep}
            />

            <VideoFeed
              settings={preferences.videoSettings}
              onVideoFrame={handleVideoFrame}
              onSettingsChange={(settings) => setPreferences({
                ...preferences,
                videoSettings: settings
              })}
            />

            <SettingsPanel
              preferences={preferences}
              characters={defaultCharacters}
              onPreferencesChange={setPreferences}
              onCharacterChange={handleCharacterChange}
            />
          </div>
        </div>

        <div className="right-panel">
          <ChatInterface
            messages={messages}
            onSendMessage={handleSendMessage}
            isProcessing={isProcessing}
            currentCharacter={currentCharacter}
          />

          <div className="controls-section">
            <div className="animation-controls">
              <h3>Test Animations:</h3>
              <button onClick={() => setAnimation('happy')}>Happy</button>
              <button onClick={() => setAnimation('excited')}>Excited</button>
              <button onClick={() => setAnimation('thinking')}>Thinking</button>
              <button onClick={() => setAnimation('confused')}>Confused</button>
              <button onClick={() => setAnimation('blink')}>Blink</button>
            </div>

            <div className="state-controls">
              <h3>Test Functions:</h3>
              <button onClick={simulateSpeaking} disabled={isSpeaking}>
                {isSpeaking ? 'Speaking...' : 'Test Speech'}
              </button>
              <button onClick={simulateProcessing} disabled={isProcessing}>
                {isProcessing ? 'Processing...' : 'Test Processing'}
              </button>
              <button onClick={clearConversation}>Clear Chat</button>
            </div>

            {aiError && (
              <div className="error-display">
                <h3>AI Error:</h3>
                <p>{aiError}</p>
              </div>
            )}

            <div className="character-info">
              <h3>Character Info:</h3>
              <p><strong>Name:</strong> {currentCharacter.name}</p>
              <p><strong>Current Animation:</strong> {currentAnimation}</p>
              <p><strong>Is Animating:</strong> {isAnimating ? 'Yes' : 'No'}</p>
              <p><strong>Is Processing:</strong> {isProcessing ? 'Yes' : 'No'}</p>
              <p><strong>Is Speaking:</strong> {isSpeaking ? 'Yes' : 'No'}</p>
              <p><strong>Is Listening:</strong> {isListening ? 'Yes' : 'No'}</p>
              <p><strong>Is Awake:</strong> {isAwake ? 'Yes' : 'No'}</p>
              <p><strong>Voice Processing:</strong> {voiceProcessing ? 'Yes' : 'No'}</p>
              <p><strong>Messages:</strong> {messages.length}</p>
              {currentTranscript && (
                <p><strong>Current Speech:</strong> "{currentTranscript}"</p>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

export default App;

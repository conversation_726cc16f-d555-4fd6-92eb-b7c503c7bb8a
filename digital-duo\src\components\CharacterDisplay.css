.character-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border-radius: 15px;
  border: 2px solid #00ffff;
  box-shadow: 
    0 0 20px rgba(0, 255, 255, 0.3),
    inset 0 0 20px rgba(0, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.character-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 0, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

.character-container {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.character-sprite {
  width: 256px;
  height: 256px;
  object-fit: contain;
  filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.5));
  transition: all 0.3s ease;
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.2);
  padding: 10px;
}

.character-sprite.animating {
  transform: scale(1.05);
  filter:
    drop-shadow(0 0 15px rgba(0, 255, 255, 0.8))
    brightness(1.1);
}

.character-sprite.talking {
  /* Add a subtle pulsing effect during talking */
  animation: talkingPulse 0.5s ease-in-out infinite alternate;
}

.character-sprite:hover {
  transform: scale(1.02);
  filter: 
    drop-shadow(0 0 12px rgba(0, 255, 255, 0.6))
    brightness(1.05);
}

.character-info {
  text-align: center;
  color: #00ffff;
  font-family: 'Courier New', monospace;
}

.character-name {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
  letter-spacing: 2px;
  text-transform: uppercase;
}

.character-status {
  margin: 5px 0 0 0;
  font-size: 0.9rem;
  opacity: 0.8;
  text-transform: capitalize;
  color: #88ffff;
}

.animation-indicator {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 3;
}

.pulse {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #00ff00;
  opacity: 0.3;
  transition: all 0.3s ease;
}

.pulse.active {
  opacity: 1;
  animation: pulse-animation 1s infinite;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.8);
}

@keyframes pulse-animation {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .character-display {
    padding: 15px;
    margin: 10px;
  }
  
  .character-sprite {
    width: 200px;
    height: 200px;
  }
  
  .character-name {
    font-size: 1.2rem;
  }
  
  .character-status {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .character-sprite {
    width: 150px;
    height: 150px;
  }
  
  .character-name {
    font-size: 1rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .character-sprite,
  .pulse {
    animation: none;
    transition: none;
  }
  
  .character-sprite.animating {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .character-display {
    border-color: #ffffff;
    background: #000000;
  }
  
  .character-name,
  .character-status {
    color: #ffffff;
    text-shadow: none;
  }
  
  .pulse.active {
    background: #ffffff;
  }
}

/* Fallback display for missing sprites */
.sprite-fallback {
  width: 256px;
  height: 256px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border: 2px solid #00ffff;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-family: 'Courier New', monospace;
  color: #00ffff;
  text-align: center;
}

.fallback-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.fallback-character {
  font-size: 1.5rem;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
}

.fallback-state {
  font-size: 1rem;
  opacity: 0.8;
  text-transform: uppercase;
  color: #88ffff;
}

/* Talking animation */
@keyframes talkingPulse {
  0% {
    filter:
      drop-shadow(0 0 10px rgba(0, 255, 255, 0.5))
      brightness(1.0);
  }
  100% {
    filter:
      drop-shadow(0 0 20px rgba(255, 255, 0, 0.7))
      brightness(1.2);
  }
}

// Utility to generate placeholder sprite images using Canvas API
export const generatePlaceholderSprite = (
  characterName: string,
  animationState: string,
  color: string = '#00ffff'
): string => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  if (!ctx) return '';
  
  canvas.width = 256;
  canvas.height = 256;
  
  // Clear canvas
  ctx.clearRect(0, 0, 256, 256);
  
  // Draw background circle
  ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
  ctx.beginPath();
  ctx.arc(128, 128, 120, 0, 2 * Math.PI);
  ctx.fill();
  
  // Draw character outline
  ctx.strokeStyle = color;
  ctx.lineWidth = 3;
  ctx.beginPath();
  ctx.arc(128, 128, 100, 0, 2 * Math.PI);
  ctx.stroke();
  
  // Draw face based on animation state
  drawFace(ctx, animationState, color);
  
  // Add character name
  ctx.fillStyle = color;
  ctx.font = 'bold 16px monospace';
  ctx.textAlign = 'center';
  ctx.fillText(characterName, 128, 240);
  
  // Add animation state indicator
  ctx.font = '12px monospace';
  ctx.fillText(animationState.toUpperCase(), 128, 20);
  
  return canvas.toDataURL('image/png');
};

const drawFace = (ctx: CanvasRenderingContext2D, state: string, color: string) => {
  ctx.fillStyle = color;
  ctx.strokeStyle = color;
  ctx.lineWidth = 2;
  
  switch (state) {
    case 'idle':
      // Eyes
      ctx.fillRect(100, 100, 8, 8);
      ctx.fillRect(148, 100, 8, 8);
      // Mouth
      ctx.beginPath();
      ctx.arc(128, 140, 15, 0, Math.PI);
      ctx.stroke();
      break;
      
    case 'blink':
      // Closed eyes
      ctx.fillRect(100, 104, 8, 2);
      ctx.fillRect(148, 104, 8, 2);
      // Mouth
      ctx.beginPath();
      ctx.arc(128, 140, 15, 0, Math.PI);
      ctx.stroke();
      break;
      
    case 'talk1':
    case 'talk2':
    case 'talk3':
      // Eyes
      ctx.fillRect(100, 100, 8, 8);
      ctx.fillRect(148, 100, 8, 8);
      // Open mouth (different sizes for talk frames)
      const mouthSize = state === 'talk2' ? 20 : 15;
      ctx.beginPath();
      ctx.ellipse(128, 145, mouthSize, 10, 0, 0, 2 * Math.PI);
      ctx.stroke();
      break;
      
    case 'happy':
      // Eyes (curved)
      ctx.beginPath();
      ctx.arc(104, 100, 4, 0, Math.PI);
      ctx.stroke();
      ctx.beginPath();
      ctx.arc(152, 100, 4, 0, Math.PI);
      ctx.stroke();
      // Smile
      ctx.beginPath();
      ctx.arc(128, 130, 20, 0, Math.PI);
      ctx.stroke();
      break;
      
    case 'excited':
      // Wide eyes
      ctx.fillRect(95, 95, 12, 12);
      ctx.fillRect(145, 95, 12, 12);
      // Big smile
      ctx.beginPath();
      ctx.arc(128, 125, 25, 0, Math.PI);
      ctx.stroke();
      // Excitement lines
      ctx.beginPath();
      ctx.moveTo(80, 80);
      ctx.lineTo(85, 75);
      ctx.moveTo(176, 80);
      ctx.lineTo(171, 75);
      ctx.stroke();
      break;
      
    case 'thinking':
      // Eyes looking up
      ctx.fillRect(100, 95, 8, 8);
      ctx.fillRect(148, 95, 8, 8);
      // Neutral mouth
      ctx.fillRect(120, 140, 16, 2);
      // Thought bubble
      ctx.beginPath();
      ctx.arc(160, 70, 8, 0, 2 * Math.PI);
      ctx.stroke();
      ctx.beginPath();
      ctx.arc(170, 60, 4, 0, 2 * Math.PI);
      ctx.stroke();
      break;
      
    case 'confused':
      // Tilted eyes
      ctx.save();
      ctx.translate(104, 104);
      ctx.rotate(-0.2);
      ctx.fillRect(-4, -4, 8, 8);
      ctx.restore();
      ctx.save();
      ctx.translate(152, 104);
      ctx.rotate(0.2);
      ctx.fillRect(-4, -4, 8, 8);
      ctx.restore();
      // Wavy mouth
      ctx.beginPath();
      ctx.moveTo(115, 140);
      ctx.quadraticCurveTo(128, 135, 141, 140);
      ctx.stroke();
      // Question mark
      ctx.font = 'bold 20px monospace';
      ctx.fillText('?', 170, 80);
      break;
      
    default:
      // Default idle face
      ctx.fillRect(100, 100, 8, 8);
      ctx.fillRect(148, 100, 8, 8);
      ctx.beginPath();
      ctx.arc(128, 140, 15, 0, Math.PI);
      ctx.stroke();
  }
};

// Character color schemes
export const characterColors = {
  aria: '#00ffff',    // Cyan
  zephyr: '#ff00ff',  // Magenta
  sage: '#ffff00'     // Yellow
};

// Generate all sprites for a character
export const generateCharacterSprites = (characterId: string, characterName: string) => {
  const color = characterColors[characterId as keyof typeof characterColors] || '#00ffff';
  const states = ['idle', 'blink', 'talk1', 'talk2', 'talk3', 'happy', 'excited', 'thinking', 'confused'];
  
  const sprites: Record<string, string> = {};
  
  states.forEach(state => {
    sprites[state] = generatePlaceholderSprite(characterName, state, color);
  });
  
  return sprites;
};

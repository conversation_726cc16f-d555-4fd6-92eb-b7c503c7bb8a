import { geminiService } from './geminiService';
import { ollamaService } from './ollamaService';

export type AIProvider = 'gemini' | 'ollama';

export interface AIServiceConfig {
  provider: AIProvider;
  gemini?: {
    apiKey?: string;
    model?: string;
  };
  ollama?: {
    baseUrl?: string;
    model?: string;
  };
}

export interface AIGenerateOptions {
  temperature?: number;
  maxTokens?: number;
  includeContext?: boolean;
  model?: string;
}

class UnifiedAIService {
  private config: AIServiceConfig;

  constructor(config: AIServiceConfig) {
    this.config = config;
    this.initializeServices();
  }

  // Initialize services based on configuration
  private initializeServices(): void {
    if (this.config.ollama?.baseUrl) {
      ollamaService.setBaseUrl(this.config.ollama.baseUrl);
    }
    if (this.config.ollama?.model) {
      ollamaService.setDefaultModel(this.config.ollama.model);
    }
  }

  // Update configuration
  updateConfig(config: Partial<AIServiceConfig>): void {
    this.config = { ...this.config, ...config };
    this.initializeServices();
  }

  // Get current provider
  getCurrentProvider(): AIProvider {
    return this.config.provider;
  }

  // Check if current AI service is available
  async checkConnection(): Promise<boolean> {
    try {
      switch (this.config.provider) {
        case 'gemini':
          return await geminiService.checkConnection();
        case 'ollama':
          return await ollamaService.checkConnection();
        default:
          return false;
      }
    } catch (error) {
      console.error('AI service connection check failed:', error);
      return false;
    }
  }

  // Generate text response using current provider
  async generateText(
    prompt: string,
    systemPrompt?: string,
    options?: AIGenerateOptions
  ): Promise<string> {
    try {
      switch (this.config.provider) {
        case 'gemini':
          return await geminiService.generateText(prompt, systemPrompt, {
            temperature: options?.temperature,
            maxTokens: options?.maxTokens,
            includeContext: options?.includeContext,
          });
        
        case 'ollama':
          return await ollamaService.generateText(prompt, systemPrompt, {
            model: options?.model || this.config.ollama?.model,
            temperature: options?.temperature,
            maxTokens: options?.maxTokens,
            includeContext: options?.includeContext,
          });
        
        default:
          throw new Error(`Unsupported AI provider: ${this.config.provider}`);
      }
    } catch (error) {
      console.error(`${this.config.provider} generateText error:`, error);
      throw error;
    }
  }

  // Generate response with image input
  async generateWithImage(
    prompt: string,
    imageData: ImageData,
    systemPrompt?: string,
    options?: AIGenerateOptions
  ): Promise<string> {
    try {
      let imageBase64: string;
      
      switch (this.config.provider) {
        case 'gemini':
          imageBase64 = geminiService.imageDataToBase64(imageData);
          return await geminiService.generateWithImage(
            prompt,
            imageBase64,
            'image/jpeg',
            systemPrompt
          );
        
        case 'ollama':
          imageBase64 = ollamaService.imageDataToBase64(imageData);
          return await ollamaService.generateWithImage(
            prompt,
            imageBase64,
            systemPrompt,
            {
              model: options?.model || this.config.ollama?.model || 'gemma3:4b', // Use current model instead of llava
            }
          );
        
        default:
          throw new Error(`Unsupported AI provider: ${this.config.provider}`);
      }
    } catch (error) {
      console.error(`${this.config.provider} generateWithImage error:`, error);
      throw error;
    }
  }

  // Get available models for current provider
  async getAvailableModels(): Promise<Array<{ id: string; name: string; description?: string }>> {
    try {
      switch (this.config.provider) {
        case 'gemini':
          // Gemini models are predefined
          return [
            { id: 'gemini-2.0-flash-exp', name: 'Gemini 2.0 Flash Experimental', description: 'Latest experimental model' },
            { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', description: 'Advanced reasoning model' },
            { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', description: 'Fast and efficient model' },
          ];
        
        case 'ollama':
          const ollamaModels = await ollamaService.getAvailableModels();
          return ollamaModels.map(model => ({
            id: model.name,
            name: model.name,
            description: `${model.details.family} - ${model.details.parameter_size}`,
          }));
        
        default:
          return [];
      }
    } catch (error) {
      console.error(`Error getting models for ${this.config.provider}:`, error);
      return [];
    }
  }

  // Get service status and information
  async getServiceInfo(): Promise<{
    provider: AIProvider;
    isConnected: boolean;
    currentModel?: string;
    availableModels: number;
    serviceUrl?: string;
  }> {
    const isConnected = await this.checkConnection();
    const availableModels = await this.getAvailableModels();

    const info = {
      provider: this.config.provider,
      isConnected,
      availableModels: availableModels.length,
    };

    switch (this.config.provider) {
      case 'gemini':
        return {
          ...info,
          currentModel: 'gemini-2.0-flash-exp',
          serviceUrl: 'Google Gemini API',
        };
      
      case 'ollama':
        const ollamaStatus = ollamaService.getServiceStatus();
        return {
          ...info,
          currentModel: ollamaStatus.defaultModel,
          serviceUrl: ollamaStatus.baseUrl,
        };
      
      default:
        return info;
    }
  }

  // Pull/install model (Ollama only)
  async pullModel(modelName: string): Promise<void> {
    if (this.config.provider === 'ollama') {
      await ollamaService.pullModel(modelName);
    } else {
      throw new Error('Model pulling is only supported for Ollama');
    }
  }

  // Delete model (Ollama only)
  async deleteModel(modelName: string): Promise<void> {
    if (this.config.provider === 'ollama') {
      await ollamaService.deleteModel(modelName);
    } else {
      throw new Error('Model deletion is only supported for Ollama');
    }
  }

  // Set model for current provider
  setModel(modelName: string): void {
    switch (this.config.provider) {
      case 'ollama':
        ollamaService.setDefaultModel(modelName);
        this.config.ollama = { ...this.config.ollama, model: modelName };
        break;
      case 'gemini':
        // For Gemini, we would need to update the service URL
        // This is handled in the geminiService itself
        break;
    }
  }

  // Clear conversation context
  clearContext(): void {
    switch (this.config.provider) {
      case 'ollama':
        ollamaService.clearContext();
        break;
      case 'gemini':
        // Gemini doesn't maintain context in the same way
        break;
    }
  }

  // Get provider-specific capabilities
  getCapabilities(): {
    supportsImages: boolean;
    supportsStreaming: boolean;
    supportsModelManagement: boolean;
    supportsLocalExecution: boolean;
  } {
    switch (this.config.provider) {
      case 'gemini':
        return {
          supportsImages: true,
          supportsStreaming: false,
          supportsModelManagement: false,
          supportsLocalExecution: false,
        };
      
      case 'ollama':
        return {
          supportsImages: true, // With llava models
          supportsStreaming: true,
          supportsModelManagement: true,
          supportsLocalExecution: true,
        };
      
      default:
        return {
          supportsImages: false,
          supportsStreaming: false,
          supportsModelManagement: false,
          supportsLocalExecution: false,
        };
    }
  }
}

// Create default instance
export const aiService = new UnifiedAIService({
  provider: 'ollama', // Default to Ollama to avoid API limits
  gemini: {
    model: 'gemini-2.0-flash-exp',
  },
  ollama: {
    baseUrl: 'http://localhost:11434',
    model: 'gemma3:4b',
  },
});

export default aiService;

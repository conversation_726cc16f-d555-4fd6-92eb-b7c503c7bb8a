import { useState, useEffect, useRef, useCallback } from 'react';
import type { VoiceInterfaceProps } from '../types';
import './VoiceInterface.css';

const VoiceInterface: React.FC<VoiceInterfaceProps> = ({
  settings,
  onVoiceInput,
  onSettingsChange,
  isListening,
  isSpeaking
}) => {
  const [isSupported, setIsSupported] = useState(false);
  const [availableVoices, setAvailableVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [audioLevel, setAudioLevel] = useState(0);
  const [error, setError] = useState<string | null>(null);
  
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const synthRef = useRef<SpeechSynthesis | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const microphoneRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const animationFrameRef = useRef<number | null>(null);

  // Initialize speech recognition and synthesis
  useEffect(() => {
    const SpeechRecognition = window.SpeechRecognition || (window as any).webkitSpeechRecognition;
    const speechSynthesis = window.speechSynthesis;

    if (SpeechRecognition && speechSynthesis) {
      setIsSupported(true);
      synthRef.current = speechSynthesis;
      
      // Initialize speech recognition
      const recognition = new SpeechRecognition();
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';
      
      recognition.onresult = (event) => {
        let finalTranscript = '';
        
        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          }
        }
        
        if (finalTranscript.trim()) {
          onVoiceInput(finalTranscript.trim());
        }
      };
      
      recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setError(`Speech recognition error: ${event.error}`);
      };
      
      recognition.onend = () => {
        if (settings.continuousListening && settings.enabled) {
          setTimeout(() => {
            try {
              recognition.start();
            } catch (e) {
              console.warn('Failed to restart recognition:', e);
            }
          }, 100);
        }
      };
      
      recognitionRef.current = recognition;
      
      // Load available voices
      const loadVoices = () => {
        const voices = speechSynthesis.getVoices();
        setAvailableVoices(voices);
      };
      
      loadVoices();
      speechSynthesis.onvoiceschanged = loadVoices;
    } else {
      setIsSupported(false);
      setError('Speech recognition or synthesis not supported in this browser');
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, [settings.continuousListening, settings.enabled, onVoiceInput]);

  // Audio level monitoring
  const startAudioMonitoring = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const audioContext = new AudioContext();
      const analyser = audioContext.createAnalyser();
      const microphone = audioContext.createMediaStreamSource(stream);
      
      analyser.fftSize = 256;
      microphone.connect(analyser);
      
      audioContextRef.current = audioContext;
      analyserRef.current = analyser;
      microphoneRef.current = microphone;
      
      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      
      const updateAudioLevel = () => {
        if (analyserRef.current) {
          analyserRef.current.getByteFrequencyData(dataArray);
          const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
          setAudioLevel(average / 255);
          animationFrameRef.current = requestAnimationFrame(updateAudioLevel);
        }
      };
      
      updateAudioLevel();
    } catch (err) {
      console.error('Error accessing microphone:', err);
      setError('Microphone access denied');
    }
  }, []);

  // Start/stop listening
  useEffect(() => {
    if (!isSupported || !recognitionRef.current) return;

    if (isListening && settings.enabled) {
      try {
        recognitionRef.current.start();
        if (settings.continuousListening) {
          startAudioMonitoring();
        }
      } catch (e) {
        console.warn('Recognition already started or failed to start:', e);
      }
    } else {
      try {
        recognitionRef.current.stop();
      } catch (e) {
        console.warn('Failed to stop recognition:', e);
      }
      
      if (audioContextRef.current) {
        audioContextRef.current.close();
        audioContextRef.current = null;
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      setAudioLevel(0);
    }
  }, [isListening, settings.enabled, settings.continuousListening, isSupported, startAudioMonitoring]);

  // Text-to-speech function
  const speak = useCallback((text: string) => {
    if (!synthRef.current || !settings.enabled) return;

    // Cancel any ongoing speech
    synthRef.current.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    
    // Apply voice settings
    utterance.rate = settings.voiceRate;
    utterance.pitch = settings.voicePitch;
    utterance.volume = settings.voiceVolume;
    
    // Set selected voice
    if (settings.selectedVoice) {
      const voice = availableVoices.find(v => v.name === settings.selectedVoice);
      if (voice) {
        utterance.voice = voice;
      }
    }
    
    synthRef.current.speak(utterance);
  }, [settings, availableVoices]);

  // Expose speak function to parent component
  useEffect(() => {
    (window as any).ariaSpeakFunction = speak;
  }, [speak]);

  const handleSettingChange = (key: keyof typeof settings, value: any) => {
    onSettingsChange({
      ...settings,
      [key]: value
    });
  };

  if (!isSupported) {
    return (
      <div className="voice-interface error">
        <h3>Voice Interface</h3>
        <p className="error-message">
          Speech recognition is not supported in this browser. 
          Please use Chrome, Edge, or Safari for voice features.
        </p>
      </div>
    );
  }

  return (
    <div className="voice-interface">
      <div className="voice-header">
        <h3>Voice Interface</h3>
        <div className="voice-status">
          <div className={`status-indicator ${isListening ? 'listening' : ''} ${isSpeaking ? 'speaking' : ''}`}>
            {isListening ? '🎤' : isSpeaking ? '🔊' : '⏸️'}
          </div>
          <span className="status-text">
            {isListening ? 'Listening...' : isSpeaking ? 'Speaking...' : 'Ready'}
          </span>
        </div>
      </div>

      {error && (
        <div className="error-message">
          {error}
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="audio-visualizer">
        <div className="audio-level-container">
          <div 
            className="audio-level-bar"
            style={{ width: `${audioLevel * 100}%` }}
          />
        </div>
        <span className="audio-level-text">
          Audio Level: {Math.round(audioLevel * 100)}%
        </span>
      </div>

      <div className="voice-controls">
        <div className="control-group">
          <label>
            <input
              type="checkbox"
              checked={settings.enabled}
              onChange={(e) => handleSettingChange('enabled', e.target.checked)}
            />
            Enable Voice Interface
          </label>
        </div>

        <div className="control-group">
          <label>
            <input
              type="checkbox"
              checked={settings.pushToTalk}
              onChange={(e) => handleSettingChange('pushToTalk', e.target.checked)}
              disabled={!settings.enabled}
            />
            Push to Talk Mode
          </label>
        </div>

        <div className="control-group">
          <label>
            <input
              type="checkbox"
              checked={settings.continuousListening}
              onChange={(e) => handleSettingChange('continuousListening', e.target.checked)}
              disabled={!settings.enabled || settings.pushToTalk}
            />
            Continuous Listening
          </label>
        </div>

        <div className="control-group">
          <label>Voice:</label>
          <select
            value={settings.selectedVoice || ''}
            onChange={(e) => handleSettingChange('selectedVoice', e.target.value)}
            disabled={!settings.enabled}
          >
            <option value="">Default Voice</option>
            {availableVoices.map((voice) => (
              <option key={voice.name} value={voice.name}>
                {voice.name} ({voice.lang})
              </option>
            ))}
          </select>
        </div>

        <div className="control-group">
          <label>Speech Rate: {settings.voiceRate}</label>
          <input
            type="range"
            min="0.5"
            max="2"
            step="0.1"
            value={settings.voiceRate}
            onChange={(e) => handleSettingChange('voiceRate', parseFloat(e.target.value))}
            disabled={!settings.enabled}
          />
        </div>

        <div className="control-group">
          <label>Speech Pitch: {settings.voicePitch}</label>
          <input
            type="range"
            min="0"
            max="2"
            step="0.1"
            value={settings.voicePitch}
            onChange={(e) => handleSettingChange('voicePitch', parseFloat(e.target.value))}
            disabled={!settings.enabled}
          />
        </div>

        <div className="control-group">
          <label>Speech Volume: {Math.round(settings.voiceVolume * 100)}%</label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={settings.voiceVolume}
            onChange={(e) => handleSettingChange('voiceVolume', parseFloat(e.target.value))}
            disabled={!settings.enabled}
          />
        </div>
      </div>
    </div>
  );
};

export default VoiceInterface;

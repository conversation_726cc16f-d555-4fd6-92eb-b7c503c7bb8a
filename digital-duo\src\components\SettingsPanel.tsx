import { useState } from 'react';
import type { SettingsPanelProps } from '../types';
import './SettingsPanel.css';

const SettingsPanel: React.FC<SettingsPanelProps> = ({
  preferences,
  characters,
  onPreferencesChange,
  onCharacterChange
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState<'general' | 'ai' | 'voice' | 'video' | 'privacy' | 'accessibility'>('general');

  const handlePreferenceChange = (section: string, key: string, value: any) => {
    const updatedPreferences = {
      ...preferences,
      [section]: {
        ...(preferences[section as keyof typeof preferences] as any),
        [key]: value
      }
    };
    onPreferencesChange(updatedPreferences);
  };

  const resetToDefaults = () => {
    const defaultPreferences = {
      selectedCharacter: 'aria',
      voiceSettings: {
        enabled: false,
        pushToTalk: false,
        continuousListening: true,
        voiceRate: 1.0,
        voicePitch: 1.0,
        voiceVolume: 1.0,
        selectedVoice: undefined
      },
      videoSettings: {
        enabled: false,
        cameraEnabled: false,
        resolution: 'medium' as const,
        frameRate: 15
      },
      aiSettings: {
        provider: 'ollama' as const,
        gemini: {
          model: 'gemini-2.0-flash-exp'
        },
        ollama: {
          baseUrl: 'http://localhost:11434',
          model: 'gemma3:4b'
        }
      },
      theme: 'retro' as const,
      accessibility: {
        highContrast: false,
        reducedMotion: false,
        screenReaderMode: false
      },
      privacy: {
        saveConversations: true,
        allowDataCollection: false,
        cameraPermission: false,
        microphonePermission: false
      }
    };
    onPreferencesChange(defaultPreferences);
  };

  const exportSettings = () => {
    const dataStr = JSON.stringify(preferences, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'digital-duo-settings.json';
    link.click();
    URL.revokeObjectURL(url);
  };

  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string);
        onPreferencesChange(imported);
      } catch (error) {
        alert('Invalid settings file');
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className={`settings-panel ${isExpanded ? 'expanded' : 'collapsed'}`}>
      <div className="settings-header" onClick={() => setIsExpanded(!isExpanded)}>
        <h3>Settings</h3>
        <span className="toggle-icon">{isExpanded ? '▼' : '⚙️'}</span>
      </div>

      {isExpanded && (
        <div className="settings-content">
          <div className="settings-tabs">
            <button
              className={activeTab === 'general' ? 'active' : ''}
              onClick={() => setActiveTab('general')}
            >
              General
            </button>
            <button
              className={activeTab === 'ai' ? 'active' : ''}
              onClick={() => setActiveTab('ai')}
            >
              AI Model
            </button>
            <button
              className={activeTab === 'voice' ? 'active' : ''}
              onClick={() => setActiveTab('voice')}
            >
              Voice
            </button>
            <button
              className={activeTab === 'video' ? 'active' : ''}
              onClick={() => setActiveTab('video')}
            >
              Video
            </button>
            <button
              className={activeTab === 'privacy' ? 'active' : ''}
              onClick={() => setActiveTab('privacy')}
            >
              Privacy
            </button>
            <button
              className={activeTab === 'accessibility' ? 'active' : ''}
              onClick={() => setActiveTab('accessibility')}
            >
              Accessibility
            </button>
          </div>

          <div className="settings-tab-content">
            {activeTab === 'general' && (
              <div className="tab-panel">
                <div className="setting-group">
                  <label>Theme:</label>
                  <select
                    value={preferences.theme}
                    onChange={(e) => handlePreferenceChange('theme', '', e.target.value)}
                  >
                    <option value="retro">Retro (Default)</option>
                    <option value="dark">Dark</option>
                    <option value="light">Light</option>
                  </select>
                </div>

                <div className="setting-group">
                  <label>Character:</label>
                  <select
                    value={preferences.selectedCharacter}
                    onChange={(e) => {
                      handlePreferenceChange('selectedCharacter', '', e.target.value);
                      onCharacterChange(e.target.value);
                    }}
                  >
                    {characters.map(char => (
                      <option key={char.id} value={char.id}>
                        {char.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            )}

            {activeTab === 'ai' && (
              <div className="tab-panel">
                <div className="setting-group">
                  <label>AI Provider:</label>
                  <select
                    value={preferences.aiSettings.provider}
                    onChange={(e) => handlePreferenceChange('aiSettings', 'provider', e.target.value)}
                  >
                    <option value="gemini">Google Gemini (Online)</option>
                    <option value="ollama">Ollama (Offline)</option>
                  </select>
                </div>

                {preferences.aiSettings.provider === 'gemini' && (
                  <>
                    <div className="setting-group">
                      <label>Gemini Model:</label>
                      <select
                        value={preferences.aiSettings.gemini.model}
                        onChange={(e) => handlePreferenceChange('aiSettings', 'gemini', {
                          ...preferences.aiSettings.gemini,
                          model: e.target.value
                        })}
                      >
                        <option value="gemini-2.0-flash-exp">Gemini 2.0 Flash Experimental</option>
                        <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                        <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                      </select>
                    </div>

                    <div className="ai-info">
                      <h4>🌐 Online AI Model</h4>
                      <p>• Requires internet connection</p>
                      <p>• Latest AI capabilities and knowledge</p>
                      <p>• Faster response times</p>
                      <p>• Supports multimodal input (text + images)</p>
                    </div>
                  </>
                )}

                {preferences.aiSettings.provider === 'ollama' && (
                  <>
                    <div className="setting-group">
                      <label>Ollama Server URL:</label>
                      <input
                        type="text"
                        value={preferences.aiSettings.ollama.baseUrl}
                        onChange={(e) => handlePreferenceChange('aiSettings', 'ollama', {
                          ...preferences.aiSettings.ollama,
                          baseUrl: e.target.value
                        })}
                        placeholder="http://localhost:11434"
                      />
                    </div>

                    <div className="setting-group">
                      <label>Ollama Model:</label>
                      <input
                        type="text"
                        value={preferences.aiSettings.ollama.model}
                        onChange={(e) => handlePreferenceChange('aiSettings', 'ollama', {
                          ...preferences.aiSettings.ollama,
                          model: e.target.value
                        })}
                        placeholder="gemma3:4b, llama2, mistral, codellama, etc."
                      />
                    </div>

                    <div className="ai-info">
                      <h4>🏠 Offline AI Model</h4>
                      <p>• Runs locally on your computer</p>
                      <p>• Complete privacy - no data sent online</p>
                      <p>• Requires Ollama to be installed and running</p>
                      <p>• Supports various open-source models</p>
                      <p>• May be slower depending on hardware</p>
                    </div>

                    <div className="ollama-setup">
                      <h4>📋 Ollama Setup Instructions</h4>
                      <ol>
                        <li>Download and install Ollama from <a href="https://ollama.ai" target="_blank" rel="noopener noreferrer">ollama.ai</a></li>
                        <li>Open terminal/command prompt</li>
                        <li>Run: <code>ollama pull gemma3:4b</code> (or your preferred model)</li>
                        <li>Start Ollama: <code>ollama serve</code></li>
                        <li>Verify it's running at http://localhost:11434</li>
                      </ol>
                    </div>
                  </>
                )}
              </div>
            )}

            {activeTab === 'voice' && (
              <div className="tab-panel">
                <div className="setting-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={preferences.voiceSettings.enabled}
                      onChange={(e) => handlePreferenceChange('voiceSettings', 'enabled', e.target.checked)}
                    />
                    Enable Voice Interface
                  </label>
                </div>

                <div className="setting-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={preferences.voiceSettings.pushToTalk}
                      onChange={(e) => handlePreferenceChange('voiceSettings', 'pushToTalk', e.target.checked)}
                      disabled={!preferences.voiceSettings.enabled}
                    />
                    Push to Talk Mode
                  </label>
                </div>

                <div className="setting-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={preferences.voiceSettings.continuousListening}
                      onChange={(e) => handlePreferenceChange('voiceSettings', 'continuousListening', e.target.checked)}
                      disabled={!preferences.voiceSettings.enabled || preferences.voiceSettings.pushToTalk}
                    />
                    Continuous Listening
                  </label>
                </div>

                <div className="setting-group">
                  <label>Speech Rate: {preferences.voiceSettings.voiceRate}</label>
                  <input
                    type="range"
                    min="0.5"
                    max="2"
                    step="0.1"
                    value={preferences.voiceSettings.voiceRate}
                    onChange={(e) => handlePreferenceChange('voiceSettings', 'voiceRate', parseFloat(e.target.value))}
                    disabled={!preferences.voiceSettings.enabled}
                  />
                </div>

                <div className="setting-group">
                  <label>Speech Pitch: {preferences.voiceSettings.voicePitch}</label>
                  <input
                    type="range"
                    min="0"
                    max="2"
                    step="0.1"
                    value={preferences.voiceSettings.voicePitch}
                    onChange={(e) => handlePreferenceChange('voiceSettings', 'voicePitch', parseFloat(e.target.value))}
                    disabled={!preferences.voiceSettings.enabled}
                  />
                </div>

                <div className="setting-group">
                  <label>Speech Volume: {Math.round(preferences.voiceSettings.voiceVolume * 100)}%</label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={preferences.voiceSettings.voiceVolume}
                    onChange={(e) => handlePreferenceChange('voiceSettings', 'voiceVolume', parseFloat(e.target.value))}
                    disabled={!preferences.voiceSettings.enabled}
                  />
                </div>
              </div>
            )}

            {activeTab === 'video' && (
              <div className="tab-panel">
                <div className="setting-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={preferences.videoSettings.enabled}
                      onChange={(e) => handlePreferenceChange('videoSettings', 'enabled', e.target.checked)}
                    />
                    Enable Video Interface
                  </label>
                </div>

                <div className="setting-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={preferences.videoSettings.cameraEnabled}
                      onChange={(e) => handlePreferenceChange('videoSettings', 'cameraEnabled', e.target.checked)}
                      disabled={!preferences.videoSettings.enabled}
                    />
                    Camera On
                  </label>
                </div>

                <div className="setting-group">
                  <label>Resolution:</label>
                  <select
                    value={preferences.videoSettings.resolution}
                    onChange={(e) => handlePreferenceChange('videoSettings', 'resolution', e.target.value)}
                    disabled={!preferences.videoSettings.enabled}
                  >
                    <option value="low">Low (320x240)</option>
                    <option value="medium">Medium (640x480)</option>
                    <option value="high">High (1280x720)</option>
                  </select>
                </div>

                <div className="setting-group">
                  <label>Frame Rate: {preferences.videoSettings.frameRate} FPS</label>
                  <input
                    type="range"
                    min="1"
                    max="30"
                    step="1"
                    value={preferences.videoSettings.frameRate}
                    onChange={(e) => handlePreferenceChange('videoSettings', 'frameRate', parseInt(e.target.value))}
                    disabled={!preferences.videoSettings.enabled}
                  />
                </div>
              </div>
            )}

            {activeTab === 'privacy' && (
              <div className="tab-panel">
                <div className="setting-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={preferences.privacy.saveConversations}
                      onChange={(e) => handlePreferenceChange('privacy', 'saveConversations', e.target.checked)}
                    />
                    Save Conversation History
                  </label>
                </div>

                <div className="setting-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={preferences.privacy.allowDataCollection}
                      onChange={(e) => handlePreferenceChange('privacy', 'allowDataCollection', e.target.checked)}
                    />
                    Allow Anonymous Usage Analytics
                  </label>
                </div>

                <div className="privacy-notice">
                  <h4>🔒 Privacy Information</h4>
                  <p>• All conversations are processed locally and with Google Gemini API</p>
                  <p>• Video frames are processed locally and not stored</p>
                  <p>• Voice data is processed by your browser's speech recognition</p>
                  <p>• No personal data is shared without your consent</p>
                </div>
              </div>
            )}

            {activeTab === 'accessibility' && (
              <div className="tab-panel">
                <div className="setting-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={preferences.accessibility.highContrast}
                      onChange={(e) => handlePreferenceChange('accessibility', 'highContrast', e.target.checked)}
                    />
                    High Contrast Mode
                  </label>
                </div>

                <div className="setting-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={preferences.accessibility.reducedMotion}
                      onChange={(e) => handlePreferenceChange('accessibility', 'reducedMotion', e.target.checked)}
                    />
                    Reduce Motion and Animations
                  </label>
                </div>

                <div className="setting-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={preferences.accessibility.screenReaderMode}
                      onChange={(e) => handlePreferenceChange('accessibility', 'screenReaderMode', e.target.checked)}
                    />
                    Screen Reader Optimizations
                  </label>
                </div>
              </div>
            )}
          </div>

          <div className="settings-actions">
            <button onClick={resetToDefaults} className="reset-button">
              Reset to Defaults
            </button>
            <button onClick={exportSettings} className="export-button">
              Export Settings
            </button>
            <label className="import-button">
              Import Settings
              <input
                type="file"
                accept=".json"
                onChange={importSettings}
                style={{ display: 'none' }}
              />
            </label>
          </div>
        </div>
      )}
    </div>
  );
};

export default SettingsPanel;

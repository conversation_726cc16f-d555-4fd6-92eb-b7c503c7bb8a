import type { APIError } from '../types';

interface OllamaModel {
  name: string;
  model: string;
  size: number;
  digest: string;
  details: {
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
  modified_at: string;
}

interface OllamaResponse {
  model: string;
  created_at: string;
  response: string;
  done: boolean;
  context?: number[];
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

interface OllamaGenerateRequest {
  model: string;
  prompt: string;
  system?: string;
  template?: string;
  context?: number[];
  stream?: boolean;
  raw?: boolean;
  format?: string;
  options?: {
    temperature?: number;
    top_k?: number;
    top_p?: number;
    num_predict?: number;
    stop?: string[];
  };
}

class OllamaService {
  private baseUrl: string;
  private defaultModel: string;
  private context: number[] = [];

  constructor(baseUrl: string = 'http://localhost:11434') {
    this.baseUrl = baseUrl;
    this.defaultModel = 'llama2'; // Default model, can be changed
  }

  // Check if <PERSON>lla<PERSON> is running and accessible
  async checkConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      return response.ok;
    } catch (error) {
      console.error('Ollama connection check failed:', error);
      return false;
    }
  }

  // Get list of available models
  async getAvailableModels(): Promise<OllamaModel[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch models: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data.models || [];
    } catch (error) {
      console.error('Error fetching Ollama models:', error);
      throw this.handleError(error);
    }
  }

  // Generate text response from Ollama
  async generateText(
    prompt: string,
    systemPrompt?: string,
    options?: {
      model?: string;
      temperature?: number;
      maxTokens?: number;
      includeContext?: boolean;
    }
  ): Promise<string> {
    try {
      const model = options?.model || this.defaultModel;
      
      const requestBody: OllamaGenerateRequest = {
        model,
        prompt,
        system: systemPrompt,
        stream: false,
        context: options?.includeContext ? this.context : undefined,
        options: {
          temperature: options?.temperature || 0.7,
          num_predict: options?.maxTokens || 1024,
          top_k: 40,
          top_p: 0.9,
        },
      };

      const response = await fetch(`${this.baseUrl}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Ollama API request failed: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
      }

      const data: OllamaResponse = await response.json();
      
      // Store context for conversation continuity
      if (data.context && options?.includeContext) {
        this.context = data.context;
      }

      return data.response || '';

    } catch (error) {
      console.error('Ollama API error:', error);
      throw this.handleError(error);
    }
  }

  // Generate response with image input (for multimodal models)
  async generateWithImage(
    prompt: string,
    imageBase64: string,
    systemPrompt?: string,
    options?: {
      model?: string;
    }
  ): Promise<string> {
    try {
      const model = options?.model || this.defaultModel; // Use current model instead of assuming llava
      
      const requestBody = {
        model,
        prompt,
        system: systemPrompt,
        images: [imageBase64],
        stream: false,
        options: {
          temperature: 0.7,
          num_predict: 1024,
        },
      };

      const response = await fetch(`${this.baseUrl}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Ollama API request failed: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
      }

      const data: OllamaResponse = await response.json();
      return data.response || '';

    } catch (error) {
      console.error('Ollama API with image error:', error);
      throw this.handleError(error);
    }
  }

  // Pull/download a model
  async pullModel(modelName: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/pull`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: modelName,
          stream: false,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to pull model: ${response.status} ${response.statusText}`);
      }

    } catch (error) {
      console.error('Error pulling Ollama model:', error);
      throw this.handleError(error);
    }
  }

  // Delete a model
  async deleteModel(modelName: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/delete`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: modelName,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to delete model: ${response.status} ${response.statusText}`);
      }

    } catch (error) {
      console.error('Error deleting Ollama model:', error);
      throw this.handleError(error);
    }
  }

  // Set the default model
  setDefaultModel(modelName: string): void {
    this.defaultModel = modelName;
  }

  // Get current default model
  getDefaultModel(): string {
    return this.defaultModel;
  }

  // Clear conversation context
  clearContext(): void {
    this.context = [];
  }

  // Set base URL for Ollama instance
  setBaseUrl(url: string): void {
    this.baseUrl = url;
  }

  // Get current base URL
  getBaseUrl(): string {
    return this.baseUrl;
  }

  // Convert ImageData to base64 string (same as Gemini service)
  imageDataToBase64(imageData: ImageData): string {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Failed to create canvas context');
    }

    canvas.width = imageData.width;
    canvas.height = imageData.height;
    ctx.putImageData(imageData, 0, 0);
    
    // Convert to base64 (remove data:image/png;base64, prefix)
    return canvas.toDataURL('image/jpeg', 0.8).split(',')[1];
  }

  // Handle API errors
  private handleError(error: any): APIError {
    if (error instanceof Error) {
      return {
        code: 'OLLAMA_ERROR',
        message: error.message,
        details: error
      };
    }
    
    return {
      code: 'UNKNOWN_ERROR',
      message: 'An unknown error occurred with Ollama',
      details: error
    };
  }

  // Get service status
  getServiceStatus(): { baseUrl: string; defaultModel: string; hasContext: boolean } {
    return {
      baseUrl: this.baseUrl,
      defaultModel: this.defaultModel,
      hasContext: this.context.length > 0
    };
  }
}

// Export singleton instance
export const ollamaService = new OllamaService();
export default ollamaService;

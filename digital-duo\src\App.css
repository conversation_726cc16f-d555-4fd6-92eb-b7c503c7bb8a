.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: #00ffff;
  font-family: 'Courier New', monospace;
  padding: 20px;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  border: 2px solid #00ffff;
  border-radius: 10px;
  background: rgba(0, 255, 255, 0.1);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.app-header > div {
  text-align: center;
  flex: 1;
}

.app-header h1 {
  margin: 0;
  font-size: 2.5rem;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
  letter-spacing: 3px;
}

.app-header p {
  margin: 10px 0 0 0;
  font-size: 1rem;
  opacity: 0.8;
  color: #88ffff;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.status-dot.connected {
  background: #00ff00;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.status-dot.connecting {
  background: #ffff00;
  box-shadow: 0 0 10px rgba(255, 255, 0, 0.5);
  animation: pulse 1s infinite;
}

.status-dot.disconnected {
  background: #ff0000;
  box-shadow: 0 0 10px rgba(255, 0, 0, 0.5);
}

.status-text {
  color: #88ffff;
}

.app-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  max-width: 1600px;
  margin: 0 auto;
}

.left-panel,
.right-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.character-section {
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.interface-controls {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.controls-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.controls-section > div {
  background: rgba(0, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 10px;
  padding: 20px;
}

.controls-section h3 {
  margin: 0 0 15px 0;
  color: #00ffff;
  font-size: 1.2rem;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.character-selector button,
.animation-controls button,
.state-controls button {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border: 2px solid #00ffff;
  color: #00ffff;
  padding: 10px 15px;
  margin: 5px;
  border-radius: 5px;
  cursor: pointer;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.character-selector button:hover,
.animation-controls button:hover,
.state-controls button:hover {
  background: linear-gradient(135deg, #00ffff 0%, #0088ff 100%);
  color: #000;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  transform: translateY(-2px);
}

.character-selector button.active {
  background: linear-gradient(135deg, #00ffff 0%, #0088ff 100%);
  color: #000;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
}

.character-selector button:disabled,
.animation-controls button:disabled,
.state-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.character-info p {
  margin: 8px 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.character-info strong {
  color: #88ffff;
}

.error-display {
  background: rgba(255, 0, 0, 0.1);
  border: 1px solid rgba(255, 0, 0, 0.3);
  border-radius: 10px;
  padding: 15px;
  color: #ff6666;
}

.error-display h3 {
  margin: 0 0 10px 0;
  color: #ff6666;
  font-size: 1rem;
}

.error-display p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive design */
@media (max-width: 1024px) {
  .app-main {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .app-header {
    flex-direction: column;
    gap: 15px;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .left-panel,
  .right-panel {
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .app {
    padding: 10px;
  }

  .app-header {
    padding: 15px;
  }

  .app-header h1 {
    font-size: 1.5rem;
    letter-spacing: 1px;
  }

  .controls-section > div {
    padding: 15px;
  }

  .character-selector button,
  .animation-controls button,
  .state-controls button {
    padding: 8px 12px;
    font-size: 0.8rem;
  }
}

.loading-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  color: #00ffff;
}

.loading-screen h1 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  animation: pulse 2s infinite;
}

.loading-screen p {
  font-size: 1.2rem;
  color: #88ffff;
  opacity: 0.8;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .character-selector button,
  .animation-controls button,
  .state-controls button {
    transition: none;
  }

  .character-selector button:hover,
  .animation-controls button:hover,
  .state-controls button:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .app {
    background: #000;
    color: #fff;
  }

  .app-header,
  .controls-section > div {
    border-color: #fff;
    background: rgba(255, 255, 255, 0.1);
  }

  .character-selector button,
  .animation-controls button,
  .state-controls button {
    border-color: #fff;
    color: #fff;
  }
}

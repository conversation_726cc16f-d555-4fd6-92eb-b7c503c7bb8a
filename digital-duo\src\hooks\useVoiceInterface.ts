import { useState, useCallback, useRef } from 'react';
import type { VoiceSettings } from '../types';

interface UseVoiceInterfaceReturn {
  voiceSettings: VoiceSettings;
  isListening: boolean;
  isSpeaking: boolean;
  startListening: () => void;
  stopListening: () => void;
  speak: (text: string) => void;
  stopSpeaking: () => void;
  updateVoiceSettings: (settings: VoiceSettings) => void;
  handleVoiceInput: (text: string) => void;
}

interface UseVoiceInterfaceProps {
  onVoiceInput: (text: string) => void;
  onSpeakingStateChange: (isSpeaking: boolean) => void;
}

export const useVoiceInterface = ({
  onVoiceInput,
  onSpeakingStateChange
}: UseVoiceInterfaceProps): UseVoiceInterfaceReturn => {
  const [voiceSettings, setVoiceSettings] = useState<VoiceSettings>({
    enabled: false,
    pushToTalk: false,
    continuousListening: true,
    voiceRate: 1.0,
    voicePitch: 1.0,
    voiceVolume: 1.0,
    selectedVoice: undefined
  });

  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const speakingTimeoutRef = useRef<number | null>(null);

  const startListening = useCallback(() => {
    if (voiceSettings.enabled && !isListening) {
      setIsListening(true);
    }
  }, [voiceSettings.enabled, isListening]);

  const stopListening = useCallback(() => {
    if (isListening) {
      setIsListening(false);
    }
  }, [isListening]);

  const speak = useCallback((text: string) => {
    if (!voiceSettings.enabled || !text.trim()) return;

    setIsSpeaking(true);
    onSpeakingStateChange(true);

    // Clear any existing timeout
    if (speakingTimeoutRef.current) {
      clearTimeout(speakingTimeoutRef.current);
    }

    // Use the global speak function set by VoiceInterface component
    if ((window as any).ariaSpeakFunction) {
      (window as any).ariaSpeakFunction(text);
    }

    // Estimate speaking duration (rough calculation: ~150 words per minute)
    const words = text.split(' ').length;
    const estimatedDuration = (words / 150) * 60 * 1000; // Convert to milliseconds
    const adjustedDuration = estimatedDuration / voiceSettings.voiceRate; // Adjust for speech rate

    // Set timeout to reset speaking state
    speakingTimeoutRef.current = setTimeout(() => {
      setIsSpeaking(false);
      onSpeakingStateChange(false);
    }, Math.max(adjustedDuration, 1000)); // Minimum 1 second

  }, [voiceSettings.enabled, voiceSettings.voiceRate, onSpeakingStateChange]);

  const stopSpeaking = useCallback(() => {
    if (window.speechSynthesis) {
      window.speechSynthesis.cancel();
    }
    
    if (speakingTimeoutRef.current) {
      clearTimeout(speakingTimeoutRef.current);
      speakingTimeoutRef.current = null;
    }
    
    setIsSpeaking(false);
    onSpeakingStateChange(false);
  }, [onSpeakingStateChange]);

  const updateVoiceSettings = useCallback((settings: VoiceSettings) => {
    setVoiceSettings(settings);
    
    // If voice is disabled, stop any ongoing operations
    if (!settings.enabled) {
      stopListening();
      stopSpeaking();
    }
  }, [stopListening, stopSpeaking]);

  const handleVoiceInput = useCallback((text: string) => {
    if (text.trim()) {
      // Stop listening temporarily while processing
      if (voiceSettings.continuousListening) {
        setIsListening(false);
        setTimeout(() => {
          if (voiceSettings.enabled && voiceSettings.continuousListening) {
            setIsListening(true);
          }
        }, 1000); // Resume listening after 1 second
      }
      
      onVoiceInput(text);
    }
  }, [voiceSettings.enabled, voiceSettings.continuousListening, onVoiceInput]);

  return {
    voiceSettings,
    isListening,
    isSpeaking,
    startListening,
    stopListening,
    speak,
    stopSpeaking,
    updateVoiceSettings,
    handleVoiceInput
  };
};

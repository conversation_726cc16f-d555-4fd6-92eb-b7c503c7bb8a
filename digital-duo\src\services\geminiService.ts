import type { GeminiAPIResponse, APIError } from '../types';

const GEMINI_API_KEY = 'AIzaSyCcwVp-O5P-_oAYclr_rSitdaefm-4NA0Q';
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent';

interface GeminiRequestBody {
  contents: Array<{
    parts: Array<{
      text?: string;
      inline_data?: {
        mime_type: string;
        data: string;
      };
    }>;
  }>;
  generationConfig?: {
    temperature?: number;
    topK?: number;
    topP?: number;
    maxOutputTokens?: number;
  };
  safetySettings?: Array<{
    category: string;
    threshold: string;
  }>;
}

class GeminiService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = GEMINI_API_KEY;
    this.baseUrl = GEMINI_API_URL;
  }

  // Generate text response from Gemini
  async generateText(
    prompt: string,
    systemPrompt?: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
      includeContext?: boolean;
    }
  ): Promise<string> {
    try {
      const fullPrompt = systemPrompt 
        ? `${systemPrompt}\n\nUser: ${prompt}`
        : prompt;

      const requestBody: GeminiRequestBody = {
        contents: [
          {
            parts: [
              {
                text: fullPrompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: options?.temperature || 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: options?.maxTokens || 1024
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          }
        ]
      };

      const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API request failed: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
      }

      const data: GeminiAPIResponse = await response.json();
      
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('No response generated from Gemini API');
      }

      const candidate = data.candidates[0];
      if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
        throw new Error('Invalid response format from Gemini API');
      }

      return candidate.content.parts[0].text || '';

    } catch (error) {
      console.error('Gemini API error:', error);
      throw this.handleError(error);
    }
  }

  // Generate response with image input (for video frames)
  async generateWithImage(
    prompt: string,
    imageData: string,
    mimeType: string = 'image/jpeg',
    systemPrompt?: string
  ): Promise<string> {
    try {
      const fullPrompt = systemPrompt 
        ? `${systemPrompt}\n\nUser: ${prompt}`
        : prompt;

      const requestBody: GeminiRequestBody = {
        contents: [
          {
            parts: [
              {
                text: fullPrompt
              },
              {
                inline_data: {
                  mime_type: mimeType,
                  data: imageData
                }
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024
        }
      };

      const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API request failed: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
      }

      const data: GeminiAPIResponse = await response.json();
      
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('No response generated from Gemini API');
      }

      const candidate = data.candidates[0];
      if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
        throw new Error('Invalid response format from Gemini API');
      }

      return candidate.content.parts[0].text || '';

    } catch (error) {
      console.error('Gemini API with image error:', error);
      throw this.handleError(error);
    }
  }

  // Convert ImageData to base64 string
  imageDataToBase64(imageData: ImageData): string {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Failed to create canvas context');
    }

    canvas.width = imageData.width;
    canvas.height = imageData.height;
    ctx.putImageData(imageData, 0, 0);
    
    // Convert to base64 (remove data:image/png;base64, prefix)
    return canvas.toDataURL('image/jpeg', 0.8).split(',')[1];
  }

  // Check API connection
  async checkConnection(): Promise<boolean> {
    try {
      await this.generateText('Hello', undefined, { maxTokens: 10 });
      return true;
    } catch (error) {
      console.error('Gemini API connection check failed:', error);
      return false;
    }
  }

  // Handle API errors
  private handleError(error: any): APIError {
    if (error instanceof Error) {
      return {
        code: 'API_ERROR',
        message: error.message,
        details: error
      };
    }
    
    return {
      code: 'UNKNOWN_ERROR',
      message: 'An unknown error occurred',
      details: error
    };
  }

  // Get API status
  getApiStatus(): { hasApiKey: boolean; apiUrl: string } {
    return {
      hasApiKey: !!this.apiKey,
      apiUrl: this.baseUrl
    };
  }
}

// Export singleton instance
export const geminiService = new GeminiService();
export default geminiService;

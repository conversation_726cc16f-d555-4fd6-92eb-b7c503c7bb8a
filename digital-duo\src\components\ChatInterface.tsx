import { useState, useRef, useEffect } from 'react';
import type { ChatInterfaceProps } from '../types';
import './ChatInterface.css';

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  messages,
  onSendMessage,
  isProcessing,
  currentCharacter
}) => {
  const [inputText, setInputText] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when expanded
  useEffect(() => {
    if (isExpanded && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isExpanded]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputText.trim() && !isProcessing) {
      onSendMessage(inputText.trim());
      setInputText('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getMessageIcon = (sender: 'user' | 'ai', emotion?: string) => {
    if (sender === 'user') {
      return '👤';
    }
    
    switch (emotion) {
      case 'happy':
        return '😊';
      case 'excited':
        return '🤩';
      case 'thinking':
        return '🤔';
      case 'confused':
        return '😕';
      default:
        return '🤖';
    }
  };

  return (
    <div className={`chat-interface ${isExpanded ? 'expanded' : 'collapsed'}`}>
      <div className="chat-header" onClick={() => setIsExpanded(!isExpanded)}>
        <h3>Chat with {currentCharacter.name}</h3>
        <div className="chat-toggle">
          <span className="toggle-icon">{isExpanded ? '▼' : '▲'}</span>
          <span className="message-count">
            {messages.length} message{messages.length !== 1 ? 's' : ''}
          </span>
        </div>
      </div>

      {isExpanded && (
        <div className="chat-content">
          <div className="messages-container">
            {messages.length === 0 ? (
              <div className="welcome-message">
                <div className="welcome-icon">👋</div>
                <p>Hello! I'm {currentCharacter.name}.</p>
                <p>{currentCharacter.description}</p>
                <p>How can I help you today?</p>
              </div>
            ) : (
              messages.map((message) => (
                <div
                  key={message.id}
                  className={`message ${message.sender === 'user' ? 'user-message' : 'ai-message'}`}
                >
                  <div className="message-header">
                    <span className="message-icon">
                      {getMessageIcon(message.sender, message.emotion)}
                    </span>
                    <span className="message-sender">
                      {message.sender === 'user' ? 'You' : currentCharacter.name}
                    </span>
                    <span className="message-timestamp">
                      {formatTimestamp(message.timestamp)}
                    </span>
                  </div>
                  <div className="message-content">
                    {message.content}
                  </div>
                </div>
              ))
            )}
            
            {isProcessing && (
              <div className="message ai-message processing">
                <div className="message-header">
                  <span className="message-icon">🤖</span>
                  <span className="message-sender">{currentCharacter.name}</span>
                  <span className="message-timestamp">Now</span>
                </div>
                <div className="message-content">
                  <div className="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          <form className="chat-input-form" onSubmit={handleSubmit}>
            <div className="input-container">
              <input
                ref={inputRef}
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={`Type a message to ${currentCharacter.name}...`}
                disabled={isProcessing}
                className="chat-input"
              />
              <button
                type="submit"
                disabled={!inputText.trim() || isProcessing}
                className="send-button"
              >
                {isProcessing ? '⏳' : '📤'}
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default ChatInterface;

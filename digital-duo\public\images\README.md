# Character Sprites Setup

## Folder Structure

Place your character sprite images in the following structure:

```
public/images/
└── aria/
    ├── idle.png
    ├── blink.png
    ├── talk1.png
    ├── talk2.png
    ├── talk3.png
    ├── happy.png
    ├── excited.png
    ├── thinking.png
    └── confused.png
```

## Character Information

### ARIA (aria folder)
- **Description**: Advanced Reasoning Intelligence Assistant, Creative Companion & Entertainment Specialist, Knowledge Keeper & Learning Mentor
- **Specialization**: General AI Assistant, Creative Arts & Entertainment, Education & Learning
- **Style**: Adaptive (Analytical, Enthusiastic, Formal based on context)
- **Color Theme**: <PERSON><PERSON> (#00ffff)

## Sprite Requirements

- **Format**: PNG with transparent background
- **Recommended Size**: 256x256 pixels
- **Naming**: Exact filenames as shown above (case-sensitive)
- **Content**: Each sprite should show the character in the corresponding emotional/action state

## Animation States

1. **idle.png** - Default resting state
2. **blink.png** - Eyes closed for blinking animation
3. **talk1.png** - First frame of talking animation (mouth slightly open)
4. **talk2.png** - Second frame of talking animation (mouth more open)
5. **talk3.png** - Third frame of talking animation (mouth wide open)
6. **happy.png** - Happy/positive emotional state
7. **excited.png** - Excited/high energy state
8. **thinking.png** - Processing/contemplating state
9. **confused.png** - Confused/uncertain state

## Usage

Once you place your images in the correct folders, the application will automatically load them. If any image fails to load, a fallback display will show the character name and current animation state.

## Testing

After placing your images, refresh the browser at http://localhost:5173/ to see your character sprites in action!

/*
  @license
	Rollup.js v4.45.0
	Sat, 12 Jul 2025 05:53:06 GMT - commit b7c7c1159f70ebe8ad6f94c942ebab2fa59c7982

	https://github.com/rollup/rollup

	Released under the MIT License.
*/
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

require('./native.js');
const parseAst_js = require('./shared/parseAst.js');
require('node:path');



exports.parseAst = parseAst_js.parseAst;
exports.parseAstAsync = parseAst_js.parseAstAsync;
//# sourceMappingURL=parseAst.js.map

{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/characterdisplay.tsx", "../../src/components/chatinterface.tsx", "../../src/components/realtimevoiceinterface.tsx", "../../src/components/settingspanel.tsx", "../../src/components/videofeed.tsx", "../../src/components/voiceinterface.tsx", "../../src/hooks/useaiconversation.ts", "../../src/hooks/usecharacteranimation.ts", "../../src/hooks/uselocalstorage.ts", "../../src/hooks/userealtimevoice.ts", "../../src/hooks/usevoiceinterface.ts", "../../src/services/geminiservice.ts", "../../src/types/index.ts", "../../src/utils/characters.ts"], "version": "5.8.3"}
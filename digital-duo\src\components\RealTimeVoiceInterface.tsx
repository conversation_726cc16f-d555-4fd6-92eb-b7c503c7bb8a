import { useState, useEffect } from 'react';
import type { VoiceSettings } from '../types';
import './RealTimeVoiceInterface.css';

interface RealTimeVoiceInterfaceProps {
  voiceSettings: VoiceSettings;
  onSettingsChange: (settings: VoiceSettings) => void;
  isListening: boolean;
  isSpeaking: boolean;
  isAwake: boolean;
  isProcessing: boolean;
  currentTranscript: string;
  onToggleVoice: () => void;
  onWakeUp: () => void;
  onSleep: () => void;
}

const RealTimeVoiceInterface: React.FC<RealTimeVoiceInterfaceProps> = ({
  voiceSettings,
  onSettingsChange,
  isListening,
  isSpeaking,
  isAwake,
  isProcessing,
  currentTranscript,
  onToggleVoice,
  onWakeUp,
  onSleep
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [availableVoices, setAvailableVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [permissionStatus, setPermissionStatus] = useState<'granted' | 'denied' | 'prompt'>('prompt');

  // Load available voices
  useEffect(() => {
    const loadVoices = () => {
      const voices = speechSynthesis.getVoices();
      setAvailableVoices(voices);
    };
    
    loadVoices();
    speechSynthesis.onvoiceschanged = loadVoices;
  }, []);

  // Check microphone permission
  useEffect(() => {
    const checkPermission = async () => {
      try {
        const result = await navigator.permissions.query({ name: 'microphone' as PermissionName });
        setPermissionStatus(result.state);
        
        result.onchange = () => {
          setPermissionStatus(result.state);
        };
      } catch (error) {
        console.warn('Permission API not supported');
      }
    };
    
    checkPermission();
  }, []);

  const handleSettingChange = (key: keyof VoiceSettings, value: any) => {
    onSettingsChange({
      ...voiceSettings,
      [key]: value
    });
  };

  const requestMicrophonePermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      setPermissionStatus('granted');
    } catch (error) {
      setPermissionStatus('denied');
      console.error('Microphone permission denied:', error);
    }
  };

  const getStatusText = () => {
    if (!voiceSettings.enabled) return 'Voice Disabled';
    if (permissionStatus === 'denied') return 'Permission Denied';
    if (permissionStatus === 'prompt') return 'Permission Needed';
    if (!isListening) return 'Not Listening';
    if (isSpeaking) return 'ARIA Speaking...';
    if (isProcessing) return 'Processing...';
    if (isAwake) return 'Listening (Awake)';
    return 'Listening for "Hello Aria"';
  };

  const getStatusIcon = () => {
    if (!voiceSettings.enabled) return '🔇';
    if (permissionStatus === 'denied') return '❌';
    if (permissionStatus === 'prompt') return '🔐';
    if (isSpeaking) return '🔊';
    if (isProcessing) return '⚡';
    if (isAwake) return '👂';
    if (isListening) return '🎤';
    return '😴';
  };

  return (
    <div className={`realtime-voice-interface ${isExpanded ? 'expanded' : 'collapsed'}`}>
      <div className="voice-header" onClick={() => setIsExpanded(!isExpanded)}>
        <h3>Real-Time Voice</h3>
        <div className="voice-status">
          <div className={`status-indicator ${isAwake ? 'awake' : ''} ${isListening ? 'listening' : ''} ${isSpeaking ? 'speaking' : ''} ${isProcessing ? 'processing' : ''}`}>
            {getStatusIcon()}
          </div>
          <span className="status-text">{getStatusText()}</span>
        </div>
      </div>

      {isExpanded && (
        <div className="voice-content">
          {permissionStatus === 'prompt' && (
            <div className="permission-request">
              <p>🎤 Microphone access is required for voice interaction</p>
              <button onClick={requestMicrophonePermission} className="permission-button">
                Grant Microphone Permission
              </button>
            </div>
          )}

          {permissionStatus === 'denied' && (
            <div className="permission-denied">
              <p>❌ Microphone access was denied</p>
              <p>Please enable microphone access in your browser settings to use voice features.</p>
            </div>
          )}

          {permissionStatus === 'granted' && (
            <>
              <div className="voice-controls">
                <div className="main-control">
                  <button
                    onClick={onToggleVoice}
                    className={`voice-toggle ${voiceSettings.enabled ? 'enabled' : 'disabled'}`}
                  >
                    {voiceSettings.enabled ? '🔊 Voice ON' : '🔇 Voice OFF'}
                  </button>
                </div>

                {voiceSettings.enabled && (
                  <div className="wake-controls">
                    <button
                      onClick={onWakeUp}
                      disabled={isAwake}
                      className="wake-button"
                    >
                      👂 Wake Up ARIA
                    </button>
                    <button
                      onClick={onSleep}
                      disabled={!isAwake}
                      className="sleep-button"
                    >
                      😴 Put ARIA to Sleep
                    </button>
                  </div>
                )}
              </div>

              {currentTranscript && (
                <div className="current-transcript">
                  <h4>Current Speech:</h4>
                  <p>"{currentTranscript}"</p>
                </div>
              )}

              <div className="wake-word-info">
                <h4>Voice Commands:</h4>
                <div className="command-list">
                  <div className="command-item">
                    <strong>Wake Words:</strong> "Hello Aria", "Hey Aria", "Hi Aria"
                  </div>
                  <div className="command-item">
                    <strong>Sleep Words:</strong> "Goodbye Aria", "Good bye Aria", "Bye Aria"
                  </div>
                </div>
              </div>

              <div className="voice-settings">
                <div className="setting-group">
                  <label>Voice:</label>
                  <select
                    value={voiceSettings.selectedVoice || ''}
                    onChange={(e) => handleSettingChange('selectedVoice', e.target.value || undefined)}
                    disabled={!voiceSettings.enabled}
                  >
                    <option value="">Default Voice</option>
                    {availableVoices.map((voice) => (
                      <option key={voice.name} value={voice.name}>
                        {voice.name} ({voice.lang})
                      </option>
                    ))}
                  </select>
                </div>

                <div className="setting-group">
                  <label>Speech Rate: {voiceSettings.voiceRate}</label>
                  <input
                    type="range"
                    min="0.5"
                    max="2"
                    step="0.1"
                    value={voiceSettings.voiceRate}
                    onChange={(e) => handleSettingChange('voiceRate', parseFloat(e.target.value))}
                    disabled={!voiceSettings.enabled}
                  />
                </div>

                <div className="setting-group">
                  <label>Speech Pitch: {voiceSettings.voicePitch}</label>
                  <input
                    type="range"
                    min="0"
                    max="2"
                    step="0.1"
                    value={voiceSettings.voicePitch}
                    onChange={(e) => handleSettingChange('voicePitch', parseFloat(e.target.value))}
                    disabled={!voiceSettings.enabled}
                  />
                </div>

                <div className="setting-group">
                  <label>Speech Volume: {Math.round(voiceSettings.voiceVolume * 100)}%</label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={voiceSettings.voiceVolume}
                    onChange={(e) => handleSettingChange('voiceVolume', parseFloat(e.target.value))}
                    disabled={!voiceSettings.enabled}
                  />
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default RealTimeVoiceInterface;

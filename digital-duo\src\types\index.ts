// Character Animation Types
export type AnimationState = 
  | 'idle'
  | 'blink'
  | 'talk1'
  | 'talk2'
  | 'talk3'
  | 'happy'
  | 'excited'
  | 'thinking'
  | 'confused';

export interface CharacterSprites {
  idle: string;
  blink: string;
  talk1: string;
  talk2: string;
  talk3: string;
  happy: string;
  excited: string;
  thinking: string;
  confused: string;
}

// Character Personality Types
export interface CharacterPersonality {
  id: string;
  name: string;
  description: string;
  sprites: CharacterSprites;
  traits: string[];
  specialization: string;
  communicationStyle: 'formal' | 'casual' | 'enthusiastic' | 'analytical' | 'adaptive';
  backstory: string;
}

// Voice Interface Types
export interface VoiceSettings {
  enabled: boolean;
  pushToTalk: boolean;
  continuousListening: boolean;
  voiceRate: number;
  voicePitch: number;
  voiceVolume: number;
  selectedVoice: string | undefined;
}

// Video Interface Types
export interface VideoSettings {
  enabled: boolean;
  cameraEnabled: boolean;
  resolution: 'low' | 'medium' | 'high';
  frameRate: number;
}

export interface AISettings {
  provider: 'gemini' | 'ollama';
  gemini: {
    model: string;
  };
  ollama: {
    baseUrl: string;
    model: string;
  };
}

// AI Integration Types
export interface AIMessage {
  id: string;
  content: string;
  timestamp: Date;
  sender: 'user' | 'ai';
  characterId: string;
  emotion?: AnimationState;
}

export interface ConversationHistory {
  messages: AIMessage[];
  characterId: string;
  sessionId: string;
  startTime: Date;
  lastActivity: Date;
}

// User Preferences Types
export interface UserPreferences {
  selectedCharacter: string;
  voiceSettings: VoiceSettings;
  videoSettings: VideoSettings;
  aiSettings: AISettings;
  theme: 'light' | 'dark' | 'retro';
  accessibility: {
    highContrast: boolean;
    reducedMotion: boolean;
    screenReaderMode: boolean;
  };
  privacy: {
    saveConversations: boolean;
    allowDataCollection: boolean;
    cameraPermission: boolean;
    microphonePermission: boolean;
  };
}

// Application State Types
export interface AppState {
  currentCharacter: CharacterPersonality;
  isListening: boolean;
  isSpeaking: boolean;
  isProcessing: boolean;
  currentAnimation: AnimationState;
  conversationHistory: ConversationHistory[];
  userPreferences: UserPreferences;
  connectionStatus: 'connected' | 'disconnected' | 'connecting';
  error?: string;
}

// API Types
export interface GeminiAPIResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
    finishReason: string;
  }>;
  usageMetadata?: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}

export interface APIError {
  code: string;
  message: string;
  details?: any;
}

// Component Props Types
export interface CharacterDisplayProps {
  character: CharacterPersonality;
  currentAnimation: AnimationState;
  isAnimating: boolean;
  onAnimationComplete?: () => void;
}

export interface VoiceInterfaceProps {
  settings: VoiceSettings;
  onVoiceInput: (text: string) => void;
  onSettingsChange: (settings: VoiceSettings) => void;
  isListening: boolean;
  isSpeaking: boolean;
}

export interface VideoFeedProps {
  settings: VideoSettings;
  onVideoFrame?: (imageData: ImageData) => void;
  onSettingsChange: (settings: VideoSettings) => void;
}

export interface ChatInterfaceProps {
  messages: AIMessage[];
  onSendMessage: (message: string) => void;
  isProcessing: boolean;
  currentCharacter: CharacterPersonality;
}

export interface SettingsPanelProps {
  preferences: UserPreferences;
  characters: CharacterPersonality[];
  onPreferencesChange: (preferences: UserPreferences) => void;
  onCharacterChange: (characterId: string) => void;
}

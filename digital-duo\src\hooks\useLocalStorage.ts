import { useState, useCallback } from 'react';

// Generic local storage hook
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  // Get from local storage then parse stored json or return initialValue
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  const setValue = useCallback((value: T | ((val: T) => T)) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      
      // Save state
      setStoredValue(valueToStore);
      
      // Save to local storage
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error);
    }
  }, [key, storedValue]);

  // Remove from localStorage
  const removeValue = useCallback(() => {
    try {
      window.localStorage.removeItem(key);
      setStoredValue(initialValue);
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  return [storedValue, setValue, removeValue];
}

// Specific hook for user preferences
export function useUserPreferences() {
  const [preferences, setPreferences, clearPreferences] = useLocalStorage('digital-duo-preferences', {
    selectedCharacter: 'aria',
    voiceSettings: {
      enabled: false,
      pushToTalk: false,
      continuousListening: true,
      voiceRate: 1.0,
      voicePitch: 1.0,
      voiceVolume: 1.0,
      selectedVoice: undefined as string | undefined
    },
    videoSettings: {
      enabled: false,
      cameraEnabled: false,
      resolution: 'medium' as 'low' | 'medium' | 'high',
      frameRate: 15
    },
    theme: 'retro' as 'light' | 'dark' | 'retro',
    accessibility: {
      highContrast: false,
      reducedMotion: false,
      screenReaderMode: false
    },
    privacy: {
      saveConversations: true,
      allowDataCollection: false,
      cameraPermission: false,
      microphonePermission: false
    }
  });

  return { preferences, setPreferences, clearPreferences };
}

// Hook for conversation history
export function useConversationHistory() {
  const [conversations, setConversations, clearConversations] = useLocalStorage<any[]>('digital-duo-conversations', []);

  const addConversation = useCallback((conversation: any) => {
    setConversations((prev: any[]) => {
      const updated = [...prev, conversation];
      // Keep only last 50 conversations to prevent storage bloat
      return updated.slice(-50);
    });
  }, [setConversations]);

  const updateConversation = useCallback((sessionId: string, updates: any) => {
    setConversations((prev: any[]) => 
      prev.map(conv => 
        conv.sessionId === sessionId 
          ? { ...conv, ...updates, lastActivity: new Date() }
          : conv
      )
    );
  }, [setConversations]);

  const getConversation = useCallback((sessionId: string) => {
    return conversations.find((conv: any) => conv.sessionId === sessionId);
  }, [conversations]);

  const deleteConversation = useCallback((sessionId: string) => {
    setConversations((prev: any[]) => prev.filter(conv => conv.sessionId !== sessionId));
  }, [setConversations]);

  return {
    conversations,
    addConversation,
    updateConversation,
    getConversation,
    deleteConversation,
    clearConversations
  };
}

// Hook for user interaction patterns
export function useUserAnalytics() {
  const [analytics, setAnalytics, clearAnalytics] = useLocalStorage('digital-duo-analytics', {
    totalInteractions: 0,
    favoriteTopics: [],
    preferredCommunicationStyle: 'adaptive',
    averageSessionLength: 0,
    mostUsedFeatures: [],
    emotionalResponses: {
      happy: 0,
      excited: 0,
      thinking: 0,
      confused: 0,
      idle: 0
    },
    lastInteraction: null
  });

  const trackInteraction = useCallback((type: string, data?: any) => {
    setAnalytics((prev: any) => ({
      ...prev,
      totalInteractions: prev.totalInteractions + 1,
      lastInteraction: new Date(),
      // Add specific tracking logic based on type
      ...(type === 'emotion' && data?.emotion ? {
        emotionalResponses: {
          ...prev.emotionalResponses,
          [data.emotion]: (prev.emotionalResponses[data.emotion] || 0) + 1
        }
      } : {}),
      ...(type === 'feature' && data?.feature ? {
        mostUsedFeatures: updateFeatureUsage(prev.mostUsedFeatures, data.feature)
      } : {})
    }));
  }, [setAnalytics]);

  const updateFeatureUsage = (features: any[], feature: string) => {
    const existing = features.find(f => f.name === feature);
    if (existing) {
      return features.map(f => 
        f.name === feature ? { ...f, count: f.count + 1 } : f
      );
    } else {
      return [...features, { name: feature, count: 1 }].slice(-20); // Keep top 20
    }
  };

  const getPersonalizedPrompt = useCallback(() => {
    const { favoriteTopics, preferredCommunicationStyle, emotionalResponses } = analytics;
    
    let prompt = '';
    
    if (favoriteTopics.length > 0) {
      prompt += `The user has shown interest in: ${favoriteTopics.slice(0, 3).join(', ')}. `;
    }
    
    if (preferredCommunicationStyle && preferredCommunicationStyle !== 'adaptive') {
      prompt += `The user prefers ${preferredCommunicationStyle} communication style. `;
    }
    
    // Find most common emotional response
    const mostCommonEmotion = Object.entries(emotionalResponses)
      .sort(([,a], [,b]) => (b as number) - (a as number))[0];
    
    if (mostCommonEmotion && mostCommonEmotion[1] > 5) {
      prompt += `The user often responds well to ${mostCommonEmotion[0]} interactions. `;
    }
    
    return prompt;
  }, [analytics]);

  return {
    analytics,
    trackInteraction,
    getPersonalizedPrompt,
    clearAnalytics
  };
}

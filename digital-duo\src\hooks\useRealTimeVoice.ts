import { useState, useEffect, useRef, useCallback } from 'react';
import type { VoiceSettings } from '../types';

interface UseRealTimeVoiceProps {
  voiceSettings: VoiceSettings;
  onWakeWordDetected: () => void;
  onGoodbyeDetected: () => void;
  onVoiceInput: (text: string) => void;
  onSpeakingStateChange: (isSpeaking: boolean) => void;
}

interface UseRealTimeVoiceReturn {
  isListening: boolean;
  isSpeaking: boolean;
  isAwake: boolean;
  isProcessing: boolean;
  currentTranscript: string;
  startListening: () => void;
  stopListening: () => void;
  speak: (text: string) => void;
  stopSpeaking: () => void;
  wakeUp: () => void;
  sleep: () => void;
}

export const useRealTimeVoice = ({
  voiceSettings,
  onWakeWordDetected,
  onGoodbyeDetected,
  onVoiceInput,
  onSpeakingStateChange
}: UseRealTimeVoiceProps): UseRealTimeVoiceReturn => {
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isAwake, setIsAwake] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState('');

  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const synthRef = useRef<SpeechSynthesis | null>(null);
  const speakingTimeoutRef = useRef<number | null>(null);
  const silenceTimeoutRef = useRef<number | null>(null);
  const restartTimeoutRef = useRef<number | null>(null);
  const isRecognitionActiveRef = useRef<boolean>(false);
  const shouldRestartRef = useRef<boolean>(true);
  const lastSpeechEndRef = useRef<number>(0);
  const isInitializingRef = useRef<boolean>(false);
  const recognitionStateRef = useRef<'stopped' | 'starting' | 'running' | 'stopping'>('stopped');

  // Wake words and goodbye phrases
  const WAKE_WORDS = ['hello aria', 'hey aria', 'hi aria'];
  const GOODBYE_WORDS = ['goodbye aria', 'good bye aria', 'bye aria', 'see you later aria'];

  // Initialize speech recognition and synthesis
  useEffect(() => {
    const SpeechRecognition = window.SpeechRecognition || (window as any).webkitSpeechRecognition;
    const speechSynthesis = window.speechSynthesis;

    if (SpeechRecognition && speechSynthesis) {
      synthRef.current = speechSynthesis;
      
      const recognition = new SpeechRecognition();
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';
      recognition.maxAlternatives = 1;

      recognition.onstart = () => {
        console.log('Voice recognition started');
        setIsListening(true);
        isRecognitionActiveRef.current = true;
        recognitionStateRef.current = 'running';
        isInitializingRef.current = false;
      };

      recognition.onresult = (event) => {
        let interimTranscript = '';
        let finalTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript.toLowerCase().trim();
          
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        const fullTranscript = (finalTranscript + interimTranscript).toLowerCase().trim();
        setCurrentTranscript(fullTranscript);

        // Check timing since last speech to prevent echo
        const timeSinceLastSpeech = Date.now() - lastSpeechEndRef.current;

        // Check for wake words when sleeping (but not if we just spoke)
        if (!isAwake && finalTranscript && timeSinceLastSpeech > 2000) {
          const containsWakeWord = WAKE_WORDS.some(word =>
            finalTranscript.includes(word)
          );

          if (containsWakeWord) {
            console.log('Wake word detected:', finalTranscript);
            wakeUp();
            onWakeWordDetected();
            setCurrentTranscript('');
            return;
          }
        }

        // Check for goodbye words when awake (but not if we just spoke)
        if (isAwake && finalTranscript && timeSinceLastSpeech > 2000) {
          const containsGoodbye = GOODBYE_WORDS.some(word =>
            finalTranscript.includes(word)
          );

          if (containsGoodbye) {
            console.log('Goodbye detected:', finalTranscript);
            sleep();
            onGoodbyeDetected();
            setCurrentTranscript('');
            return;
          }
        }

        // Process voice input when awake and we have final transcript
        // Also check that enough time has passed since ARIA last spoke to avoid echo
        if (isAwake && finalTranscript && !isSpeaking && timeSinceLastSpeech > 2000) {
          console.log('Processing voice input:', finalTranscript);
          setIsProcessing(true);

          // Stop listening while processing to prevent interference
          stopListening();

          onVoiceInput(finalTranscript);
          setCurrentTranscript('');

          // Reset processing state after a timeout
          setTimeout(() => setIsProcessing(false), 5000);
        } else if (isAwake && finalTranscript && timeSinceLastSpeech <= 2000) {
          console.log('Ignoring potential echo:', finalTranscript);
          setCurrentTranscript('');
        }

        // Reset silence timeout on any speech
        if (fullTranscript) {
          resetSilenceTimeout();
        }
      };

      recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        isRecognitionActiveRef.current = false;
        recognitionStateRef.current = 'stopped';

        // Don't restart on certain errors
        if (event.error === 'not-allowed' || event.error === 'service-not-allowed') {
          setIsListening(false);
          shouldRestartRef.current = false;
          return;
        }

        // Handle aborted errors (usually caused by stopping/starting too quickly)
        if (event.error === 'aborted') {
          console.log('Recognition aborted, will restart if needed');
          // Don't immediately restart on abort - wait for proper timing
          return;
        }

        // For other errors, mark that we should restart but with delay
        shouldRestartRef.current = true;
      };

      recognition.onend = () => {
        console.log('Voice recognition ended');
        setIsListening(false);
        isRecognitionActiveRef.current = false;
        recognitionStateRef.current = 'stopped';

        // Only restart if we should and enough time has passed since last speech
        const now = Date.now();
        const timeSinceLastSpeech = now - lastSpeechEndRef.current;

        if (shouldRestartRef.current && voiceSettings.enabled && !isSpeaking && !isInitializingRef.current && timeSinceLastSpeech > 2000) {
          // Clear any existing restart timeout
          if (restartTimeoutRef.current) {
            clearTimeout(restartTimeoutRef.current);
          }

          restartTimeoutRef.current = window.setTimeout(() => {
            if (shouldRestartRef.current && voiceSettings.enabled && !isSpeaking && !isRecognitionActiveRef.current && recognitionStateRef.current === 'stopped') {
              try {
                console.log('Restarting recognition after proper delay...');
                recognitionStateRef.current = 'starting';
                recognition.start();
              } catch (e) {
                console.warn('Failed to restart recognition:', e);
                isRecognitionActiveRef.current = false;
                recognitionStateRef.current = 'stopped';
              }
            }
          }, 1500); // Longer delay to prevent conflicts
        }
      };

      recognitionRef.current = recognition;
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      clearTimeouts();
    };
  }, [voiceSettings.enabled, isAwake, isSpeaking, onWakeWordDetected, onGoodbyeDetected, onVoiceInput]);

  // Clear all timeouts
  const clearTimeouts = useCallback(() => {
    if (speakingTimeoutRef.current) {
      clearTimeout(speakingTimeoutRef.current);
      speakingTimeoutRef.current = null;
    }
    if (silenceTimeoutRef.current) {
      clearTimeout(silenceTimeoutRef.current);
      silenceTimeoutRef.current = null;
    }
    if (restartTimeoutRef.current) {
      clearTimeout(restartTimeoutRef.current);
      restartTimeoutRef.current = null;
    }
  }, []);

  // Reset silence timeout (auto-sleep after period of inactivity)
  const resetSilenceTimeout = useCallback(() => {
    if (silenceTimeoutRef.current) {
      clearTimeout(silenceTimeoutRef.current);
    }
    
    if (isAwake) {
      silenceTimeoutRef.current = window.setTimeout(() => {
        console.log('Auto-sleeping due to inactivity');
        sleep();
      }, 30000); // 30 seconds of silence
    }
  }, [isAwake]);

  // Start listening
  const startListening = useCallback(() => {
    if (!voiceSettings.enabled || !recognitionRef.current || isRecognitionActiveRef.current || recognitionStateRef.current !== 'stopped') {
      return;
    }

    try {
      console.log('Starting recognition...');
      shouldRestartRef.current = true;
      recognitionStateRef.current = 'starting';
      isInitializingRef.current = true;
      recognitionRef.current.start();
    } catch (e) {
      console.warn('Recognition already started or failed to start:', e);
      isRecognitionActiveRef.current = false;
      recognitionStateRef.current = 'stopped';
      isInitializingRef.current = false;
    }
  }, [voiceSettings.enabled]);

  // Stop listening
  const stopListening = useCallback(() => {
    console.log('Stopping recognition...');
    shouldRestartRef.current = false;
    recognitionStateRef.current = 'stopping';

    if (recognitionRef.current && isRecognitionActiveRef.current) {
      try {
        recognitionRef.current.stop();
      } catch (e) {
        console.warn('Error stopping recognition:', e);
      }
    }

    setIsListening(false);
    isRecognitionActiveRef.current = false;
    recognitionStateRef.current = 'stopped';
    isInitializingRef.current = false;
    clearTimeouts();
  }, [clearTimeouts]);

  // Speak text
  const speak = useCallback((text: string) => {
    if (!synthRef.current || !voiceSettings.enabled || !text.trim()) return;

    console.log('ARIA starting to speak:', text);

    // Stop any ongoing speech and recognition
    synthRef.current.cancel();
    stopListening();

    // Record when speech ends for timing
    lastSpeechEndRef.current = Date.now();

    const utterance = new SpeechSynthesisUtterance(text);

    // Apply voice settings
    utterance.rate = voiceSettings.voiceRate;
    utterance.pitch = voiceSettings.voicePitch;
    utterance.volume = voiceSettings.voiceVolume;

    // Set selected voice
    if (voiceSettings.selectedVoice) {
      const voices = synthRef.current.getVoices();
      const voice = voices.find(v => v.name === voiceSettings.selectedVoice);
      if (voice) {
        utterance.voice = voice;
      }
    }

    utterance.onstart = () => {
      console.log('ARIA speech started');
      setIsSpeaking(true);
      onSpeakingStateChange(true);
      setIsProcessing(false);
    };

    utterance.onend = () => {
      console.log('ARIA speech ended');
      setIsSpeaking(false);
      onSpeakingStateChange(false);
      lastSpeechEndRef.current = Date.now();

      // Resume listening after a longer delay to prevent echo
      if (voiceSettings.enabled) {
        setTimeout(() => {
          console.log('Resuming listening after speech...');
          startListening();
        }, 1500); // Increased delay to prevent echo
      }
    };

    utterance.onerror = (event) => {
      console.error('Speech synthesis error:', event);
      setIsSpeaking(false);
      onSpeakingStateChange(false);
      lastSpeechEndRef.current = Date.now();

      // Resume listening on error
      if (voiceSettings.enabled) {
        setTimeout(() => {
          startListening();
        }, 1000);
      }
    };

    synthRef.current.speak(utterance);
  }, [voiceSettings, onSpeakingStateChange, startListening, stopListening]);

  // Stop speaking
  const stopSpeaking = useCallback(() => {
    if (synthRef.current) {
      synthRef.current.cancel();
    }
    
    if (speakingTimeoutRef.current) {
      clearTimeout(speakingTimeoutRef.current);
      speakingTimeoutRef.current = null;
    }
    
    setIsSpeaking(false);
    onSpeakingStateChange(false);
  }, [onSpeakingStateChange]);

  // Wake up ARIA
  const wakeUp = useCallback(() => {
    setIsAwake(true);
    resetSilenceTimeout();
    console.log('ARIA is now awake and listening');
  }, [resetSilenceTimeout]);

  // Put ARIA to sleep
  const sleep = useCallback(() => {
    setIsAwake(false);
    setCurrentTranscript('');
    clearTimeouts();
    console.log('ARIA is now sleeping');
  }, [clearTimeouts]);

  // Auto-start listening when voice is enabled
  useEffect(() => {
    if (voiceSettings.enabled && !isListening && !isSpeaking && !isRecognitionActiveRef.current && !isInitializingRef.current && recognitionStateRef.current === 'stopped') {
      const timeSinceLastSpeech = Date.now() - lastSpeechEndRef.current;
      if (timeSinceLastSpeech > 3000) { // Even longer delay to prevent conflicts
        console.log('Auto-starting listening...');
        const timeoutId = setTimeout(() => {
          if (voiceSettings.enabled && !isSpeaking && !isRecognitionActiveRef.current && !isInitializingRef.current && recognitionStateRef.current === 'stopped') {
            startListening();
          }
        }, 2000); // Longer delay for stability

        // Cleanup timeout on unmount or dependency change
        return () => clearTimeout(timeoutId);
      }
    } else if (!voiceSettings.enabled) {
      stopListening();
      sleep();
    }
  }, [voiceSettings.enabled, isListening, isSpeaking, startListening, stopListening, sleep]);

  return {
    isListening,
    isSpeaking,
    isAwake,
    isProcessing,
    currentTranscript,
    startListening,
    stopListening,
    speak,
    stopSpeaking,
    wakeUp,
    sleep
  };
};

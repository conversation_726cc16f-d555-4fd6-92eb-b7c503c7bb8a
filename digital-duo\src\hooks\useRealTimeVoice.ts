import { useState, useEffect, useRef, useCallback } from 'react';
import type { VoiceSettings } from '../types';

interface UseRealTimeVoiceProps {
  voiceSettings: VoiceSettings;
  onWakeWordDetected: () => void;
  onGoodbyeDetected: () => void;
  onVoiceInput: (text: string) => void;
  onSpeakingStateChange: (isSpeaking: boolean) => void;
}

interface UseRealTimeVoiceReturn {
  isListening: boolean;
  isSpeaking: boolean;
  isAwake: boolean;
  isProcessing: boolean;
  currentTranscript: string;
  startListening: () => void;
  stopListening: () => void;
  speak: (text: string) => void;
  stopSpeaking: () => void;
  wakeUp: () => void;
  sleep: () => void;
}

export const useRealTimeVoice = ({
  voiceSettings,
  onWakeWordDetected,
  onGoodbyeDetected,
  onVoiceInput,
  onSpeakingStateChange
}: UseRealTimeVoiceProps): UseRealTimeVoiceReturn => {
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isAwake, setIsAwake] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState('');

  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const synthRef = useRef<SpeechSynthesis | null>(null);
  const speakingTimeoutRef = useRef<number | null>(null);
  const silenceTimeoutRef = useRef<number | null>(null);
  const restartTimeoutRef = useRef<number | null>(null);

  // Wake words and goodbye phrases
  const WAKE_WORDS = ['hello aria', 'hey aria', 'hi aria'];
  const GOODBYE_WORDS = ['goodbye aria', 'good bye aria', 'bye aria', 'see you later aria'];

  // Initialize speech recognition and synthesis
  useEffect(() => {
    const SpeechRecognition = window.SpeechRecognition || (window as any).webkitSpeechRecognition;
    const speechSynthesis = window.speechSynthesis;

    if (SpeechRecognition && speechSynthesis) {
      synthRef.current = speechSynthesis;
      
      const recognition = new SpeechRecognition();
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';
      recognition.maxAlternatives = 1;

      recognition.onstart = () => {
        console.log('Voice recognition started');
        setIsListening(true);
      };

      recognition.onresult = (event) => {
        let interimTranscript = '';
        let finalTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript.toLowerCase().trim();
          
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        const fullTranscript = (finalTranscript + interimTranscript).toLowerCase().trim();
        setCurrentTranscript(fullTranscript);

        // Check for wake words when sleeping
        if (!isAwake && finalTranscript) {
          const containsWakeWord = WAKE_WORDS.some(word => 
            finalTranscript.includes(word)
          );
          
          if (containsWakeWord) {
            console.log('Wake word detected:', finalTranscript);
            wakeUp();
            onWakeWordDetected();
            setCurrentTranscript('');
            return;
          }
        }

        // Check for goodbye words when awake
        if (isAwake && finalTranscript) {
          const containsGoodbye = GOODBYE_WORDS.some(word => 
            finalTranscript.includes(word)
          );
          
          if (containsGoodbye) {
            console.log('Goodbye detected:', finalTranscript);
            sleep();
            onGoodbyeDetected();
            setCurrentTranscript('');
            return;
          }
        }

        // Process voice input when awake and we have final transcript
        if (isAwake && finalTranscript && !isSpeaking) {
          console.log('Processing voice input:', finalTranscript);
          setIsProcessing(true);
          onVoiceInput(finalTranscript);
          setCurrentTranscript('');
          
          // Reset processing state after a timeout
          setTimeout(() => setIsProcessing(false), 5000);
        }

        // Reset silence timeout on any speech
        if (fullTranscript) {
          resetSilenceTimeout();
        }
      };

      recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        
        // Don't restart on certain errors
        if (event.error === 'not-allowed' || event.error === 'service-not-allowed') {
          setIsListening(false);
          return;
        }
        
        // Restart recognition after a brief delay for other errors
        setTimeout(() => {
          if (voiceSettings.enabled && !isSpeaking) {
            try {
              recognition.start();
            } catch (e) {
              console.warn('Failed to restart recognition:', e);
            }
          }
        }, 1000);
      };

      recognition.onend = () => {
        console.log('Voice recognition ended');
        setIsListening(false);
        
        // Auto-restart if voice is enabled and not speaking
        if (voiceSettings.enabled && !isSpeaking) {
          restartTimeoutRef.current = window.setTimeout(() => {
            try {
              recognition.start();
            } catch (e) {
              console.warn('Failed to restart recognition:', e);
            }
          }, 100);
        }
      };

      recognitionRef.current = recognition;
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      clearTimeouts();
    };
  }, [voiceSettings.enabled, isAwake, isSpeaking, onWakeWordDetected, onGoodbyeDetected, onVoiceInput]);

  // Clear all timeouts
  const clearTimeouts = useCallback(() => {
    if (speakingTimeoutRef.current) {
      clearTimeout(speakingTimeoutRef.current);
      speakingTimeoutRef.current = null;
    }
    if (silenceTimeoutRef.current) {
      clearTimeout(silenceTimeoutRef.current);
      silenceTimeoutRef.current = null;
    }
    if (restartTimeoutRef.current) {
      clearTimeout(restartTimeoutRef.current);
      restartTimeoutRef.current = null;
    }
  }, []);

  // Reset silence timeout (auto-sleep after period of inactivity)
  const resetSilenceTimeout = useCallback(() => {
    if (silenceTimeoutRef.current) {
      clearTimeout(silenceTimeoutRef.current);
    }
    
    if (isAwake) {
      silenceTimeoutRef.current = window.setTimeout(() => {
        console.log('Auto-sleeping due to inactivity');
        sleep();
      }, 30000); // 30 seconds of silence
    }
  }, [isAwake]);

  // Start listening
  const startListening = useCallback(() => {
    if (!voiceSettings.enabled || !recognitionRef.current) return;
    
    try {
      recognitionRef.current.start();
    } catch (e) {
      console.warn('Recognition already started or failed to start:', e);
    }
  }, [voiceSettings.enabled]);

  // Stop listening
  const stopListening = useCallback(() => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    setIsListening(false);
    clearTimeouts();
  }, [clearTimeouts]);

  // Speak text
  const speak = useCallback((text: string) => {
    if (!synthRef.current || !voiceSettings.enabled || !text.trim()) return;

    // Stop any ongoing speech
    synthRef.current.cancel();
    
    // Temporarily stop listening while speaking
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
    }

    const utterance = new SpeechSynthesisUtterance(text);
    
    // Apply voice settings
    utterance.rate = voiceSettings.voiceRate;
    utterance.pitch = voiceSettings.voicePitch;
    utterance.volume = voiceSettings.voiceVolume;
    
    // Set selected voice
    if (voiceSettings.selectedVoice) {
      const voices = synthRef.current.getVoices();
      const voice = voices.find(v => v.name === voiceSettings.selectedVoice);
      if (voice) {
        utterance.voice = voice;
      }
    }

    utterance.onstart = () => {
      setIsSpeaking(true);
      onSpeakingStateChange(true);
      setIsProcessing(false);
    };

    utterance.onend = () => {
      setIsSpeaking(false);
      onSpeakingStateChange(false);
      
      // Resume listening after speaking
      if (voiceSettings.enabled) {
        setTimeout(() => {
          startListening();
        }, 500);
      }
    };

    utterance.onerror = () => {
      setIsSpeaking(false);
      onSpeakingStateChange(false);
      
      // Resume listening on error
      if (voiceSettings.enabled) {
        setTimeout(() => {
          startListening();
        }, 500);
      }
    };

    synthRef.current.speak(utterance);
  }, [voiceSettings, isListening, onSpeakingStateChange, startListening]);

  // Stop speaking
  const stopSpeaking = useCallback(() => {
    if (synthRef.current) {
      synthRef.current.cancel();
    }
    
    if (speakingTimeoutRef.current) {
      clearTimeout(speakingTimeoutRef.current);
      speakingTimeoutRef.current = null;
    }
    
    setIsSpeaking(false);
    onSpeakingStateChange(false);
  }, [onSpeakingStateChange]);

  // Wake up ARIA
  const wakeUp = useCallback(() => {
    setIsAwake(true);
    resetSilenceTimeout();
    console.log('ARIA is now awake and listening');
  }, [resetSilenceTimeout]);

  // Put ARIA to sleep
  const sleep = useCallback(() => {
    setIsAwake(false);
    setCurrentTranscript('');
    clearTimeouts();
    console.log('ARIA is now sleeping');
  }, [clearTimeouts]);

  // Auto-start listening when voice is enabled
  useEffect(() => {
    if (voiceSettings.enabled && !isListening && !isSpeaking) {
      startListening();
    } else if (!voiceSettings.enabled) {
      stopListening();
      sleep();
    }
  }, [voiceSettings.enabled, isListening, isSpeaking, startListening, stopListening, sleep]);

  return {
    isListening,
    isSpeaking,
    isAwake,
    isProcessing,
    currentTranscript,
    startListening,
    stopListening,
    speak,
    stopSpeaking,
    wakeUp,
    sleep
  };
};

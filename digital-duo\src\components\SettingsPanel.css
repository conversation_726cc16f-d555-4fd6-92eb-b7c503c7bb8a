.settings-panel {
  background: rgba(0, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 10px;
  font-family: 'Courier New', monospace;
  color: #00ffff;
  transition: all 0.3s ease;
}

.settings-panel.collapsed {
  height: auto;
}

.settings-panel.expanded {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.settings-header {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  user-select: none;
  transition: background-color 0.3s ease;
}

.settings-header:hover {
  background: rgba(0, 255, 255, 0.1);
}

.settings-header h3 {
  margin: 0;
  color: #00ffff;
  font-size: 1.2rem;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.toggle-icon {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.settings-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100% - 60px);
}

.settings-tabs {
  display: flex;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  background: rgba(0, 0, 0, 0.2);
}

.settings-tabs button {
  background: none;
  border: none;
  color: #88ffff;
  padding: 12px 16px;
  cursor: pointer;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  border-bottom: 2px solid transparent;
}

.settings-tabs button:hover {
  background: rgba(0, 255, 255, 0.1);
  color: #00ffff;
}

.settings-tabs button.active {
  color: #00ffff;
  border-bottom-color: #00ffff;
  background: rgba(0, 255, 255, 0.1);
}

.settings-tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.tab-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-group label {
  font-size: 0.9rem;
  color: #00ffff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-group input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #00ffff;
}

.setting-group input[type="range"] {
  width: 100%;
  height: 6px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 3px;
  outline: none;
  border: 1px solid rgba(0, 255, 255, 0.3);
}

.setting-group input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: #00ffff;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.setting-group input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #00ffff;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.setting-group select {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 4px;
  color: #00ffff;
  padding: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.setting-group select:focus {
  outline: none;
  border-color: #00ffff;
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.setting-group input[type="text"] {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 4px;
  color: #00ffff;
  padding: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  width: 100%;
}

.setting-group input[type="text"]:focus {
  outline: none;
  border-color: #00ffff;
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.setting-group input:disabled,
.setting-group select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.ai-info {
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.ai-info h4 {
  margin: 0 0 10px 0;
  color: #00ffff;
  font-size: 1rem;
}

.ai-info p {
  margin: 5px 0;
  font-size: 0.8rem;
  color: #88ffff;
  line-height: 1.4;
}

.ollama-setup {
  background: rgba(255, 165, 0, 0.1);
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.ollama-setup h4 {
  margin: 0 0 10px 0;
  color: #ffaa00;
  font-size: 1rem;
}

.ollama-setup ol {
  margin: 10px 0;
  padding-left: 20px;
  color: #ffcc88;
}

.ollama-setup li {
  margin: 8px 0;
  font-size: 0.8rem;
  line-height: 1.4;
}

.ollama-setup code {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 3px;
  padding: 2px 6px;
  font-family: 'Courier New', monospace;
  color: #ffaa00;
  font-size: 0.8rem;
}

.ollama-setup a {
  color: #00ffff;
  text-decoration: underline;
}

.ollama-setup a:hover {
  color: #88ffff;
}

.privacy-notice {
  background: rgba(0, 255, 0, 0.1);
  border: 1px solid rgba(0, 255, 0, 0.3);
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
}

.privacy-notice h4 {
  margin: 0 0 10px 0;
  color: #88ff88;
  font-size: 1rem;
}

.privacy-notice p {
  margin: 5px 0;
  font-size: 0.8rem;
  color: #88ff88;
  line-height: 1.4;
}

.settings-actions {
  display: flex;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid rgba(0, 255, 255, 0.2);
  background: rgba(0, 0, 0, 0.2);
}

.settings-actions button,
.settings-actions label {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border: 2px solid #00ffff;
  color: #00ffff;
  padding: 8px 12px;
  border-radius: 5px;
  cursor: pointer;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  text-align: center;
}

.settings-actions button:hover,
.settings-actions label:hover {
  background: linear-gradient(135deg, #00ffff 0%, #0088ff 100%);
  color: #000;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  transform: translateY(-1px);
}

.reset-button {
  background: linear-gradient(135deg, #ff4444 0%, #cc0000 100%) !important;
  border-color: #ff4444 !important;
}

.reset-button:hover {
  background: linear-gradient(135deg, #ff6666 0%, #ff0000 100%) !important;
  color: #fff !important;
  box-shadow: 0 0 10px rgba(255, 68, 68, 0.5) !important;
}

/* Scrollbar styling for tab content */
.settings-tab-content::-webkit-scrollbar {
  width: 6px;
}

.settings-tab-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}

.settings-tab-content::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.3);
  border-radius: 3px;
}

.settings-tab-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 255, 0.5);
}

/* Responsive design */
@media (max-width: 768px) {
  .settings-panel.expanded {
    height: 500px;
  }
  
  .settings-tabs {
    flex-wrap: wrap;
  }
  
  .settings-tabs button {
    padding: 10px 12px;
    font-size: 0.8rem;
  }
  
  .settings-tab-content {
    padding: 15px;
  }
  
  .settings-actions {
    padding: 15px;
    flex-wrap: wrap;
  }
  
  .settings-actions button,
  .settings-actions label {
    flex: 1;
    min-width: 120px;
  }
}

@media (max-width: 480px) {
  .settings-panel.expanded {
    height: 450px;
  }
  
  .settings-tabs button {
    padding: 8px 10px;
    font-size: 0.7rem;
  }
  
  .setting-group label {
    font-size: 0.8rem;
  }
  
  .privacy-notice {
    padding: 10px;
  }
  
  .privacy-notice h4 {
    font-size: 0.9rem;
  }
  
  .privacy-notice p {
    font-size: 0.7rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .settings-tabs button,
  .settings-actions button,
  .settings-actions label {
    transition: none;
  }
  
  .settings-actions button:hover,
  .settings-actions label:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .settings-panel {
    border-color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .settings-header h3,
  .setting-group label,
  .settings-tabs button {
    color: #ffffff;
    text-shadow: none;
  }
  
  .setting-group input[type="range"],
  .setting-group select {
    border-color: #ffffff;
  }
  
  .settings-tabs button.active {
    border-bottom-color: #ffffff;
  }
}

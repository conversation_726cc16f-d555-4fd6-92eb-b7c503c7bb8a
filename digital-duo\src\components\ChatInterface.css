.chat-interface {
  background: rgba(0, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 10px;
  font-family: 'Courier New', monospace;
  color: #00ffff;
  transition: all 0.3s ease;
}

.chat-interface.collapsed {
  height: auto;
}

.chat-interface.expanded {
  height: 500px;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  user-select: none;
  transition: background-color 0.3s ease;
}

.chat-header:hover {
  background: rgba(0, 255, 255, 0.1);
}

.chat-header h3 {
  margin: 0;
  color: #00ffff;
  font-size: 1.2rem;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.chat-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
  color: #88ffff;
}

.toggle-icon {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.message-count {
  font-size: 0.8rem;
  opacity: 0.8;
}

.chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100% - 60px);
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.welcome-message {
  text-align: center;
  padding: 40px 20px;
  color: #88ffff;
  opacity: 0.8;
}

.welcome-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.welcome-message p {
  margin: 8px 0;
  line-height: 1.4;
}

.message {
  display: flex;
  flex-direction: column;
  gap: 5px;
  max-width: 80%;
  animation: fadeInUp 0.3s ease;
}

.user-message {
  align-self: flex-end;
}

.ai-message {
  align-self: flex-start;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
  opacity: 0.8;
}

.message-icon {
  font-size: 1rem;
}

.message-sender {
  font-weight: bold;
  color: #00ffff;
}

.message-timestamp {
  color: #666;
  margin-left: auto;
}

.message-content {
  background: rgba(0, 0, 0, 0.3);
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid rgba(0, 255, 255, 0.2);
  line-height: 1.4;
  word-wrap: break-word;
}

.user-message .message-content {
  background: rgba(0, 255, 255, 0.1);
  border-color: rgba(0, 255, 255, 0.3);
}

.ai-message .message-content {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 255, 255, 0.2);
}

.message.processing .message-content {
  background: rgba(255, 165, 0, 0.1);
  border-color: rgba(255, 165, 0, 0.3);
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
  height: 20px;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background: #00ffff;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

.chat-input-form {
  padding: 20px;
  border-top: 1px solid rgba(0, 255, 255, 0.2);
  background: rgba(0, 0, 0, 0.2);
}

.input-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.chat-input {
  flex: 1;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 20px;
  padding: 12px 16px;
  color: #00ffff;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  outline: none;
  transition: all 0.3s ease;
}

.chat-input:focus {
  border-color: #00ffff;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.chat-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.chat-input::placeholder {
  color: #666;
}

.send-button {
  background: linear-gradient(135deg, #00ffff 0%, #0088ff 100%);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  color: #000;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-button:hover:not(:disabled) {
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Scrollbar styling for messages */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.3);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 255, 0.5);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .chat-interface.expanded {
    height: 400px;
  }
  
  .message {
    max-width: 90%;
  }
  
  .chat-header {
    padding: 12px 15px;
  }
  
  .messages-container {
    padding: 15px;
  }
  
  .chat-input-form {
    padding: 15px;
  }
  
  .welcome-message {
    padding: 30px 15px;
  }
  
  .welcome-icon {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .chat-interface.expanded {
    height: 350px;
  }
  
  .message {
    max-width: 95%;
  }
  
  .chat-header h3 {
    font-size: 1rem;
  }
  
  .message-content {
    padding: 10px 12px;
  }
  
  .chat-input {
    padding: 10px 14px;
    font-size: 0.8rem;
  }
  
  .send-button {
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .message,
  .typing-indicator span,
  .send-button {
    animation: none;
    transition: none;
  }
  
  .send-button:hover:not(:disabled) {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .chat-interface {
    border-color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .chat-header h3,
  .message-sender,
  .chat-input {
    color: #ffffff;
    text-shadow: none;
  }
  
  .message-content {
    border-color: #ffffff;
  }
  
  .chat-input {
    border-color: #ffffff;
  }
}

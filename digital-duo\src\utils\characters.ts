import type { CharacterPersonality } from '../types';

// Create sprite paths for actual image files in public/images folder
const createSpritePaths = (characterId: string) => ({
  idle: `/images/${characterId}/idle.png`,
  blink: `/images/${characterId}/blink.png`,
  talk1: `/images/${characterId}/talk1.png`,
  talk2: `/images/${characterId}/talk2.png`,
  talk3: `/images/${characterId}/talk3.png`,
  happy: `/images/${characterId}/happy.png`,
  excited: `/images/${characterId}/excited.png`,
  thinking: `/images/${characterId}/thinking.png`,
  confused: `/images/${characterId}/confused.png`,
});

export const defaultCharacters: CharacterPersonality[] = [
  {
    id: 'aria',
    name: '<PERSON>',
    description: 'Advanced Reasoning Intelligence Assistant, Creative Companion & Entertainment Specialist, Knowledge Keeper & Learning Mentor',
    sprites: createSpritePaths('aria'),
    traits: [
      'Analytical',
      'Creative',
      'Wise',
      'Helpful',
      'Energetic',
      'Patient',
      'Curious',
      'Inspiring',
      'Knowledgeable',
      'Detail-oriented',
      'Playful',
      'Encouraging',
      'Supportive',
      'Spontaneous'
    ],
    specialization: 'General AI Assistant, Creative Arts & Entertainment, Education & Learning',
    communicationStyle: 'adaptive',
    backstory: 'ARIA is a sophisticated multi-faceted AI companion that combines analytical reasoning, creative inspiration, and educational mentorship. She seamlessly adapts between being a logical problem-solver for complex analysis, an energetic creative partner for brainstorming and entertainment, and a wise patient mentor for learning and knowledge sharing. Her versatile personality allows her to excel across all domains - from technical discussions and research to creative projects, storytelling, teaching, and skill development. ARIA represents the perfect balance of intelligence, creativity, and wisdom in a single AI companion.'
  }
];

export const getCharacterById = (id: string): CharacterPersonality | undefined => {
  return defaultCharacters.find(character => character.id === id);
};

export const getDefaultCharacter = (): CharacterPersonality => {
  console.log('getDefaultCharacter called');
  console.log('defaultCharacters:', defaultCharacters);
  const defaultChar = defaultCharacters[0]; // ARIA as default
  console.log('Returning default character:', defaultChar);
  return defaultChar;
};

// Emotion detection based on AI response content
export const detectEmotionFromText = (text: string): 'happy' | 'excited' | 'thinking' | 'confused' | 'idle' => {
  const lowerText = text.toLowerCase();
  
  // Excited indicators
  if (lowerText.includes('!') || 
      lowerText.includes('amazing') || 
      lowerText.includes('fantastic') || 
      lowerText.includes('incredible') ||
      lowerText.includes('wow')) {
    return 'excited';
  }
  
  // Happy indicators
  if (lowerText.includes('great') || 
      lowerText.includes('good') || 
      lowerText.includes('nice') || 
      lowerText.includes('wonderful') ||
      lowerText.includes('excellent')) {
    return 'happy';
  }
  
  // Confused indicators
  if (lowerText.includes('?') || 
      lowerText.includes('unclear') || 
      lowerText.includes('not sure') || 
      lowerText.includes('confused') ||
      lowerText.includes('clarify')) {
    return 'confused';
  }
  
  // Thinking indicators
  if (lowerText.includes('let me think') || 
      lowerText.includes('considering') || 
      lowerText.includes('analyzing') || 
      lowerText.includes('processing') ||
      lowerText.includes('hmm')) {
    return 'thinking';
  }
  
  return 'idle';
};

// Generate personality-specific response prompts
export const getPersonalityPrompt = (character: CharacterPersonality): string => {
  const basePrompt = `You are ${character.name}, ${character.description}. `;

  const traitPrompt = `Your key traits are: ${character.traits.join(', ')}. `;

  const stylePrompt = 'You can adapt your communication style based on the context: use analytical and detailed responses for technical topics, enthusiastic and energetic language for creative discussions, and formal professional tone for educational content. ';

  const specializationPrompt = `You specialize in ${character.specialization}. `;

  const backstoryPrompt = `Background: ${character.backstory} `;

  return basePrompt + traitPrompt + stylePrompt + specializationPrompt + backstoryPrompt +
    'Respond in character while being helpful and engaging. Adapt your communication style to match the topic and user needs. Keep responses conversational and appropriate for voice interaction.';
};

import { useState, useEffect, useCallback } from 'react';
import type { CharacterDisplayProps } from '../types';
import './CharacterDisplay.css';

const CharacterDisplay: React.FC<CharacterDisplayProps> = ({
  character,
  currentAnimation,
  isAnimating,
  onAnimationComplete
}) => {
  const [currentSprite, setCurrentSprite] = useState<string>(character.sprites.idle);
  const [blinkTimer, setBlinkTimer] = useState<number | null>(null);
  const [talkAnimationFrame, setTalkAnimationFrame] = useState<number>(0);
  const [talkAnimationTimer, setTalkAnimationTimer] = useState<number | null>(null);

  // Handle blinking animation
  const triggerBlink = useCallback(() => {
    if (currentAnimation === 'idle') {
      setCurrentSprite(character.sprites.blink);
      setTimeout(() => {
        setCurrentSprite(character.sprites.idle);
      }, 150); // Blink duration
    }
    
    // Schedule next blink (3-7 seconds)
    const nextBlinkDelay = Math.random() * 4000 + 3000;
    const timer = setTimeout(triggerBlink, nextBlinkDelay);
    setBlinkTimer(timer);
  }, [character.sprites, currentAnimation]);

  // Handle talking animation cycle
  const cycleTalkAnimation = useCallback(() => {
    const talkFrames = [character.sprites.talk1, character.sprites.talk2, character.sprites.talk3];
    const frameIndex = talkAnimationFrame % talkFrames.length;
    setCurrentSprite(talkFrames[frameIndex]);
    setTalkAnimationFrame(prev => prev + 1);
    
    const timer = setTimeout(cycleTalkAnimation, 200); // 200ms per frame
    setTalkAnimationTimer(timer);
  }, [character.sprites, talkAnimationFrame]);

  // Handle animation state changes
  useEffect(() => {
    // Clear existing timers
    if (blinkTimer) {
      clearTimeout(blinkTimer);
      setBlinkTimer(null);
    }
    if (talkAnimationTimer) {
      clearTimeout(talkAnimationTimer);
      setTalkAnimationTimer(null);
    }

    switch (currentAnimation) {
      case 'idle':
        setCurrentSprite(character.sprites.idle);
        // Start blinking cycle for idle state
        const initialBlinkDelay = Math.random() * 4000 + 3000;
        const timer = setTimeout(triggerBlink, initialBlinkDelay);
        setBlinkTimer(timer);
        break;
        
      case 'blink':
        setCurrentSprite(character.sprites.blink);
        setTimeout(() => {
          setCurrentSprite(character.sprites.idle);
          onAnimationComplete?.();
        }, 150);
        break;
        
      case 'talk1':
      case 'talk2':
      case 'talk3':
        // Start talking animation cycle
        setTalkAnimationFrame(0);
        cycleTalkAnimation();
        break;
        
      case 'happy':
        setCurrentSprite(character.sprites.happy);
        break;
        
      case 'excited':
        setCurrentSprite(character.sprites.excited);
        break;
        
      case 'thinking':
        setCurrentSprite(character.sprites.thinking);
        break;
        
      case 'confused':
        setCurrentSprite(character.sprites.confused);
        break;
        
      default:
        setCurrentSprite(character.sprites.idle);
    }

    return () => {
      if (blinkTimer) clearTimeout(blinkTimer);
      if (talkAnimationTimer) clearTimeout(talkAnimationTimer);
    };
  }, [currentAnimation, character.sprites, triggerBlink, cycleTalkAnimation, onAnimationComplete]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (blinkTimer) clearTimeout(blinkTimer);
      if (talkAnimationTimer) clearTimeout(talkAnimationTimer);
    };
  }, [blinkTimer, talkAnimationTimer]);

  return (
    <div className="character-display">
      <div className="character-container">
        <img
          src={currentSprite}
          alt={`${character.name} - ${currentAnimation}`}
          className={`character-sprite ${isAnimating ? 'animating' : ''}`}
          onError={(e) => {
            // Fallback to a simple colored div if image fails to load
            console.warn(`Failed to load sprite: ${currentSprite}`);
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';

            // Show fallback element
            const fallback = target.nextElementSibling as HTMLDivElement;
            if (fallback && fallback.classList.contains('sprite-fallback')) {
              fallback.style.display = 'flex';
            }
          }}
          onLoad={() => {
            // Hide fallback when image loads successfully
            const img = document.querySelector('.character-sprite') as HTMLImageElement;
            const fallback = img?.nextElementSibling as HTMLDivElement;
            if (fallback && fallback.classList.contains('sprite-fallback')) {
              fallback.style.display = 'none';
            }
          }}
        />
        <div className="sprite-fallback" style={{ display: 'none' }}>
          <div className="fallback-content">
            <div className="fallback-character">{character.name}</div>
            <div className="fallback-state">{currentAnimation}</div>
          </div>
        </div>
        <div className="character-info">
          <h3 className="character-name">{character.name}</h3>
          <p className="character-status">{currentAnimation}</p>
        </div>
      </div>
      <div className="animation-indicator">
        <div className={`pulse ${isAnimating ? 'active' : ''}`}></div>
      </div>
    </div>
  );
};

export default CharacterDisplay;

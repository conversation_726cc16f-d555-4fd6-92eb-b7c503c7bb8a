import { useState, useEffect, useCallback } from 'react';
import type { CharacterDisplayProps } from '../types';
import './CharacterDisplay.css';

const CharacterDisplay: React.FC<CharacterDisplayProps> = ({
  character,
  currentAnimation,
  isAnimating,
  onAnimationComplete
}) => {
  const [currentSprite, setCurrentSprite] = useState<string>(character.sprites.idle);
  const [blinkTimer, setBlinkTimer] = useState<number | null>(null);

  // Handle blinking animation
  const triggerBlink = useCallback(() => {
    if (currentAnimation === 'idle') {
      setCurrentSprite(character.sprites.blink);
      setTimeout(() => {
        setCurrentSprite(character.sprites.idle);
      }, 150); // Blink duration
    }
    
    // Schedule next blink (3-7 seconds)
    const nextBlinkDelay = Math.random() * 4000 + 3000;
    const timer = setTimeout(triggerBlink, nextBlinkDelay);
    setBlinkTimer(timer);
  }, [character.sprites, currentAnimation]);



  // Handle animation state changes
  useEffect(() => {
    // Clear existing timers
    if (blinkTimer) {
      clearTimeout(blinkTimer);
      setBlinkTimer(null);
    }

    switch (currentAnimation) {
      case 'idle':
        setCurrentSprite(character.sprites.idle);
        // Start blinking cycle for idle state
        const initialBlinkDelay = Math.random() * 4000 + 3000;
        const timer = setTimeout(triggerBlink, initialBlinkDelay);
        setBlinkTimer(timer);
        break;
        
      case 'blink':
        setCurrentSprite(character.sprites.blink);
        setTimeout(() => {
          setCurrentSprite(character.sprites.idle);
          onAnimationComplete?.();
        }, 150);
        break;
        
      case 'talk1':
        setCurrentSprite(character.sprites.talk1);
        break;

      case 'talk2':
        setCurrentSprite(character.sprites.talk2);
        break;

      case 'talk3':
        setCurrentSprite(character.sprites.talk3);
        break;
        
      case 'happy':
        setCurrentSprite(character.sprites.happy);
        break;
        
      case 'excited':
        setCurrentSprite(character.sprites.excited);
        break;
        
      case 'thinking':
        setCurrentSprite(character.sprites.thinking);
        break;
        
      case 'confused':
        setCurrentSprite(character.sprites.confused);
        break;
        
      default:
        setCurrentSprite(character.sprites.idle);
    }

    return () => {
      if (blinkTimer) clearTimeout(blinkTimer);
    };
  }, [currentAnimation, character.sprites, triggerBlink, onAnimationComplete]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (blinkTimer) clearTimeout(blinkTimer);
    };
  }, [blinkTimer]);

  return (
    <div className="character-display">
      <div className="character-container">
        <img
          src={currentSprite}
          alt={`${character.name} - ${currentAnimation}`}
          className={`character-sprite ${isAnimating ? 'animating' : ''}`}
          onError={(e) => {
            // Fallback to a simple colored div if image fails to load
            console.warn(`Failed to load sprite: ${currentSprite}`);
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';

            // Show fallback element
            const fallback = target.nextElementSibling as HTMLDivElement;
            if (fallback && fallback.classList.contains('sprite-fallback')) {
              fallback.style.display = 'flex';
            }
          }}
          onLoad={() => {
            // Hide fallback when image loads successfully
            const img = document.querySelector('.character-sprite') as HTMLImageElement;
            const fallback = img?.nextElementSibling as HTMLDivElement;
            if (fallback && fallback.classList.contains('sprite-fallback')) {
              fallback.style.display = 'none';
            }
          }}
        />
        <div className="sprite-fallback" style={{ display: 'none' }}>
          <div className="fallback-content">
            <div className="fallback-character">{character.name}</div>
            <div className="fallback-state">{currentAnimation}</div>
          </div>
        </div>
        <div className="character-info">
          <h3 className="character-name">{character.name}</h3>
          <p className="character-status">{currentAnimation}</p>
        </div>
      </div>
      <div className="animation-indicator">
        <div className={`pulse ${isAnimating ? 'active' : ''}`}></div>
      </div>
    </div>
  );
};

export default CharacterDisplay;

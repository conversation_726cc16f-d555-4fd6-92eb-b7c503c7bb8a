.video-feed {
  background: rgba(0, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 10px;
  padding: 20px;
  font-family: 'Courier New', monospace;
  color: #00ffff;
}

.video-feed.error {
  border-color: rgba(255, 0, 0, 0.5);
  background: rgba(255, 0, 0, 0.05);
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.video-header h3 {
  margin: 0;
  color: #00ffff;
  font-size: 1.2rem;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.video-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  font-size: 1.2rem;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.status-indicator.streaming {
  background: rgba(255, 0, 0, 0.2);
  box-shadow: 0 0 10px rgba(255, 0, 0, 0.5);
  animation: pulse-streaming 2s infinite;
}

.status-text {
  font-size: 0.9rem;
  color: #88ffff;
}

.error-message {
  background: rgba(255, 0, 0, 0.1);
  border: 1px solid rgba(255, 0, 0, 0.3);
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 15px;
  color: #ff6666;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message button {
  background: none;
  border: none;
  color: #ff6666;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
}

.video-container {
  position: relative;
  width: 100%;
  height: 240px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 255, 0.3);
  margin-bottom: 20px;
  overflow: hidden;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.video-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
}

.placeholder-content {
  text-align: center;
  color: #88ffff;
}

.placeholder-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: 10px;
  opacity: 0.5;
}

.placeholder-content p {
  margin: 0;
  font-size: 1rem;
}

.video-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.control-group label {
  font-size: 0.9rem;
  color: #00ffff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #00ffff;
}

.control-group input[type="range"] {
  width: 100%;
  height: 6px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 3px;
  outline: none;
  border: 1px solid rgba(0, 255, 255, 0.3);
}

.control-group input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: #00ffff;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.control-group input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #00ffff;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.control-group select {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 4px;
  color: #00ffff;
  padding: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.control-group select:focus {
  outline: none;
  border-color: #00ffff;
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.control-group input:disabled,
.control-group select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.privacy-notice {
  background: rgba(0, 255, 0, 0.1);
  border: 1px solid rgba(0, 255, 0, 0.3);
  border-radius: 5px;
  padding: 10px;
  margin-top: 10px;
}

.privacy-notice p {
  margin: 0;
  font-size: 0.8rem;
  color: #88ff88;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Animations */
@keyframes pulse-streaming {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .video-feed {
    padding: 15px;
  }
  
  .video-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .video-status {
    align-self: flex-end;
  }
  
  .video-container {
    height: 180px;
  }
  
  .control-group label {
    font-size: 0.8rem;
  }
  
  .placeholder-icon {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .video-feed {
    padding: 10px;
  }
  
  .video-container {
    height: 150px;
  }
  
  .control-group {
    gap: 3px;
  }
  
  .placeholder-icon {
    font-size: 1.5rem;
  }
  
  .placeholder-content p {
    font-size: 0.9rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .status-indicator.streaming {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .video-feed {
    border-color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .video-header h3,
  .control-group label,
  .status-text {
    color: #ffffff;
    text-shadow: none;
  }
  
  .control-group input[type="range"],
  .control-group select {
    border-color: #ffffff;
  }
  
  .video-container {
    border-color: #ffffff;
  }
}

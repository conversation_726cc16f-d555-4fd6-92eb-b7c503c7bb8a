import { useState, useEffect, useRef, useCallback } from 'react';
import type { VideoFeedProps } from '../types';
import './VideoFeed.css';

const VideoFeed: React.FC<VideoFeedProps> = ({
  settings,
  onVideoFrame,
  onSettingsChange
}) => {
  const [isSupported, setIsSupported] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [devices, setDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<string>('');

  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const frameIntervalRef = useRef<number | null>(null);

  // Check for WebRTC support
  useEffect(() => {
    const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    setIsSupported(hasGetUserMedia);
    
    if (!hasGetUserMedia) {
      setError('Camera access is not supported in this browser');
    }
  }, []);

  // Get available video devices
  const getVideoDevices = useCallback(async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');
      setDevices(videoDevices);
      
      if (videoDevices.length > 0 && !selectedDevice) {
        setSelectedDevice(videoDevices[0].deviceId);
      }
    } catch (err) {
      console.error('Error enumerating devices:', err);
      setError('Failed to get camera devices');
    }
  }, [selectedDevice]);

  // Request camera permission and get devices
  useEffect(() => {
    if (isSupported) {
      getVideoDevices();
    }
  }, [isSupported, getVideoDevices]);

  // Start video stream
  const startStream = useCallback(async () => {
    if (!isSupported || !settings.enabled) return;

    try {
      const constraints: MediaStreamConstraints = {
        video: {
          deviceId: selectedDevice ? { exact: selectedDevice } : undefined,
          width: getResolutionConstraints(settings.resolution).width,
          height: getResolutionConstraints(settings.resolution).height,
          frameRate: settings.frameRate
        },
        audio: false
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
      }
      
      streamRef.current = stream;
      setIsStreaming(true);
      setHasPermission(true);
      setError(null);

      // Start frame processing if callback is provided
      if (onVideoFrame) {
        startFrameProcessing();
      }

    } catch (err: any) {
      console.error('Error starting video stream:', err);
      setError(`Camera access failed: ${err.message}`);
      setHasPermission(false);
      setIsStreaming(false);
    }
  }, [isSupported, settings.enabled, settings.resolution, settings.frameRate, selectedDevice, onVideoFrame]);

  // Stop video stream
  const stopStream = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
    
    if (frameIntervalRef.current) {
      clearInterval(frameIntervalRef.current);
      frameIntervalRef.current = null;
    }
    
    setIsStreaming(false);
  }, []);

  // Start/stop stream based on settings
  useEffect(() => {
    if (settings.enabled && settings.cameraEnabled) {
      startStream();
    } else {
      stopStream();
    }

    return () => {
      stopStream();
    };
  }, [settings.enabled, settings.cameraEnabled, startStream, stopStream]);

  // Frame processing for AI input
  const startFrameProcessing = useCallback(() => {
    if (!onVideoFrame || !videoRef.current || !canvasRef.current) return;

    const processFrame = () => {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      
      if (!video || !canvas || video.readyState !== video.HAVE_ENOUGH_DATA) return;

      const ctx = canvas.getContext('2d', { willReadFrequently: true });
      if (!ctx) return;

      // Set canvas size to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw current video frame to canvas
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Get image data and pass to callback
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      onVideoFrame(imageData);
    };

    // Process frames at specified interval
    const interval = 1000 / settings.frameRate;
    frameIntervalRef.current = setInterval(processFrame, interval);
  }, [onVideoFrame, settings.frameRate]);

  // Handle settings changes
  const handleSettingChange = (key: keyof typeof settings, value: any) => {
    onSettingsChange({
      ...settings,
      [key]: value
    });
  };

  // Get resolution constraints
  const getResolutionConstraints = (resolution: string) => {
    switch (resolution) {
      case 'low':
        return { width: 320, height: 240 };
      case 'medium':
        return { width: 640, height: 480 };
      case 'high':
        return { width: 1280, height: 720 };
      default:
        return { width: 640, height: 480 };
    }
  };

  if (!isSupported) {
    return (
      <div className="video-feed error">
        <h3>Video Feed</h3>
        <p className="error-message">
          Camera access is not supported in this browser.
        </p>
      </div>
    );
  }

  return (
    <div className="video-feed">
      <div className="video-header">
        <h3>Video Feed</h3>
        <div className="video-status">
          <div className={`status-indicator ${isStreaming ? 'streaming' : ''}`}>
            {isStreaming ? '📹' : '📷'}
          </div>
          <span className="status-text">
            {isStreaming ? 'Streaming' : hasPermission ? 'Ready' : 'No Permission'}
          </span>
        </div>
      </div>

      {error && (
        <div className="error-message">
          {error}
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="video-container">
        <video
          ref={videoRef}
          className="video-element"
          muted
          playsInline
          style={{ display: isStreaming ? 'block' : 'none' }}
        />
        <canvas
          ref={canvasRef}
          className="video-canvas"
          style={{ display: 'none' }}
        />
        {!isStreaming && (
          <div className="video-placeholder">
            <div className="placeholder-content">
              <span className="placeholder-icon">📷</span>
              <p>Camera {settings.enabled ? 'Starting...' : 'Disabled'}</p>
            </div>
          </div>
        )}
      </div>

      <div className="video-controls">
        <div className="control-group">
          <label>
            <input
              type="checkbox"
              checked={settings.enabled}
              onChange={(e) => handleSettingChange('enabled', e.target.checked)}
            />
            Enable Video Interface
          </label>
        </div>

        <div className="control-group">
          <label>
            <input
              type="checkbox"
              checked={settings.cameraEnabled}
              onChange={(e) => handleSettingChange('cameraEnabled', e.target.checked)}
              disabled={!settings.enabled}
            />
            Camera On
          </label>
        </div>

        <div className="control-group">
          <label>Camera Device:</label>
          <select
            value={selectedDevice}
            onChange={(e) => setSelectedDevice(e.target.value)}
            disabled={!settings.enabled || isStreaming}
          >
            {devices.map((device) => (
              <option key={device.deviceId} value={device.deviceId}>
                {device.label || `Camera ${device.deviceId.slice(0, 8)}`}
              </option>
            ))}
          </select>
        </div>

        <div className="control-group">
          <label>Resolution:</label>
          <select
            value={settings.resolution}
            onChange={(e) => handleSettingChange('resolution', e.target.value)}
            disabled={!settings.enabled}
          >
            <option value="low">Low (320x240)</option>
            <option value="medium">Medium (640x480)</option>
            <option value="high">High (1280x720)</option>
          </select>
        </div>

        <div className="control-group">
          <label>Frame Rate: {settings.frameRate} FPS</label>
          <input
            type="range"
            min="1"
            max="30"
            step="1"
            value={settings.frameRate}
            onChange={(e) => handleSettingChange('frameRate', parseInt(e.target.value))}
            disabled={!settings.enabled}
          />
        </div>

        {hasPermission && (
          <div className="privacy-notice">
            <p>🔒 Camera access granted. Your video is processed locally and not stored.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoFeed;

:root{font-family:Courier New,monospace;line-height:1.5;font-weight:400;color-scheme:dark;color:#0ff;background-color:#0a0a0a;font-synthesis:none;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}*{box-sizing:border-box}body{margin:0;padding:0;min-width:320px;min-height:100vh;background:linear-gradient(135deg,#0a0a0a,#1a1a2e,#16213e);overflow-x:hidden}#root{width:100%;min-height:100vh}::-webkit-scrollbar{width:8px}::-webkit-scrollbar-track{background:#0000004d}::-webkit-scrollbar-thumb{background:linear-gradient(135deg,#0ff,#08f);border-radius:4px}::-webkit-scrollbar-thumb:hover{background:linear-gradient(135deg,#0cc,#06c)}::selection{background:#00ffff4d;color:#fff}button:focus,button:focus-visible{outline:2px solid #00ffff;outline-offset:2px}@media (prefers-reduced-motion: reduce){*{animation-duration:.01ms!important;animation-iteration-count:1!important;transition-duration:.01ms!important}}@media (prefers-contrast: high){:root{color:#fff;background-color:#000}body{background:#000}}@media print{body,.app{background:#fff;color:#000}}.character-display{display:flex;flex-direction:column;align-items:center;padding:20px;background:linear-gradient(135deg,#1a1a2e,#16213e,#0f3460);border-radius:15px;border:2px solid #00ffff;box-shadow:0 0 20px #00ffff4d,inset 0 0 20px #00ffff1a;position:relative;overflow:hidden}.character-display:before{content:"";position:absolute;inset:0;background:radial-gradient(circle at 20% 20%,rgba(0,255,255,.1) 0%,transparent 50%),radial-gradient(circle at 80% 80%,rgba(255,0,255,.1) 0%,transparent 50%);pointer-events:none;z-index:1}.character-container{position:relative;z-index:2;display:flex;flex-direction:column;align-items:center;gap:15px}.character-sprite{width:256px;height:256px;object-fit:contain;filter:drop-shadow(0 0 10px rgba(0,255,255,.5));transition:all .3s ease;border-radius:10px;background:#0003;padding:10px}.character-sprite.animating{transform:scale(1.05);filter:drop-shadow(0 0 15px rgba(0,255,255,.8)) brightness(1.1)}.character-sprite:hover{transform:scale(1.02);filter:drop-shadow(0 0 12px rgba(0,255,255,.6)) brightness(1.05)}.character-info{text-align:center;color:#0ff;font-family:Courier New,monospace}.character-name{margin:0;font-size:1.5rem;font-weight:700;text-shadow:0 0 10px rgba(0,255,255,.8);letter-spacing:2px;text-transform:uppercase}.character-status{margin:5px 0 0;font-size:.9rem;opacity:.8;text-transform:capitalize;color:#8ff}.animation-indicator{position:absolute;bottom:10px;right:10px;z-index:3}.pulse{width:12px;height:12px;border-radius:50%;background:#0f0;opacity:.3;transition:all .3s ease}.pulse.active{opacity:1;animation:pulse-animation 1s infinite;box-shadow:0 0 10px #0f0c}@keyframes pulse-animation{0%{transform:scale(1);opacity:1}50%{transform:scale(1.2);opacity:.7}to{transform:scale(1);opacity:1}}@media (max-width: 768px){.character-display{padding:15px;margin:10px}.character-sprite{width:200px;height:200px}.character-name{font-size:1.2rem}.character-status{font-size:.8rem}}@media (max-width: 480px){.character-sprite{width:150px;height:150px}.character-name{font-size:1rem}}@media (prefers-reduced-motion: reduce){.character-sprite,.pulse{animation:none;transition:none}.character-sprite.animating{transform:none}}@media (prefers-contrast: high){.character-display{border-color:#fff;background:#000}.character-name,.character-status{color:#fff;text-shadow:none}.pulse.active{background:#fff}}.sprite-fallback{width:256px;height:256px;background:linear-gradient(135deg,#1a1a2e,#16213e,#0f3460);border:2px solid #00ffff;border-radius:10px;display:flex;align-items:center;justify-content:center;flex-direction:column;font-family:Courier New,monospace;color:#0ff;text-align:center}.fallback-content{display:flex;flex-direction:column;align-items:center;gap:10px}.fallback-character{font-size:1.5rem;font-weight:700;text-shadow:0 0 10px rgba(0,255,255,.8)}.fallback-state{font-size:1rem;opacity:.8;text-transform:uppercase;color:#8ff}.app{min-height:100vh;background:linear-gradient(135deg,#0a0a0a,#1a1a2e,#16213e);color:#0ff;font-family:Courier New,monospace;padding:20px}.app-header{text-align:center;margin-bottom:30px;padding:20px;border:2px solid #00ffff;border-radius:10px;background:#00ffff1a;box-shadow:0 0 20px #00ffff4d}.app-header h1{margin:0;font-size:2.5rem;text-shadow:0 0 10px rgba(0,255,255,.8);letter-spacing:3px}.app-header p{margin:10px 0 0;font-size:1rem;opacity:.8;color:#8ff}.app-main{display:grid;grid-template-columns:1fr 1fr;gap:30px;max-width:1400px;margin:0 auto}.character-section{display:flex;justify-content:center;align-items:flex-start}.controls-section{display:flex;flex-direction:column;gap:20px}.controls-section>div{background:#00ffff0d;border:1px solid rgba(0,255,255,.3);border-radius:10px;padding:20px}.controls-section h3{margin:0 0 15px;color:#0ff;font-size:1.2rem;text-shadow:0 0 5px rgba(0,255,255,.5)}.character-selector button,.animation-controls button,.state-controls button{background:linear-gradient(135deg,#1a1a2e,#16213e);border:2px solid #00ffff;color:#0ff;padding:10px 15px;margin:5px;border-radius:5px;cursor:pointer;font-family:Courier New,monospace;font-size:.9rem;transition:all .3s ease}.character-selector button:hover,.animation-controls button:hover,.state-controls button:hover{background:linear-gradient(135deg,#0ff,#08f);color:#000;box-shadow:0 0 10px #00ffff80;transform:translateY(-2px)}.character-selector button.active{background:linear-gradient(135deg,#0ff,#08f);color:#000;box-shadow:0 0 15px #0ffc}.character-selector button:disabled,.animation-controls button:disabled,.state-controls button:disabled{opacity:.5;cursor:not-allowed;transform:none}.character-info p{margin:8px 0;font-size:.9rem;line-height:1.4}.character-info strong{color:#8ff}@media (max-width: 1024px){.app-main{grid-template-columns:1fr;gap:20px}.app-header h1{font-size:2rem}}@media (max-width: 768px){.app{padding:10px}.app-header{padding:15px}.app-header h1{font-size:1.5rem;letter-spacing:1px}.controls-section>div{padding:15px}.character-selector button,.animation-controls button,.state-controls button{padding:8px 12px;font-size:.8rem}}@media (prefers-reduced-motion: reduce){.character-selector button,.animation-controls button,.state-controls button{transition:none}.character-selector button:hover,.animation-controls button:hover,.state-controls button:hover{transform:none}}@media (prefers-contrast: high){.app{background:#000;color:#fff}.app-header,.controls-section>div{border-color:#fff;background:#ffffff1a}.character-selector button,.animation-controls button,.state-controls button{border-color:#fff;color:#fff}}
